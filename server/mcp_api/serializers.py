"""
Serializzatori Django REST Framework per il protocollo MCP.

Questi serializzatori convertono i modelli Django in formato JSON
seguendo le specifiche del protocollo MCP per garantire compatibilità.

Documentazione MCP: https://modelcontextprotocol.io/
Specifiche: https://spec.modelcontextprotocol.io/
"""

from rest_framework import serializers
from .models import Tool, ToolCall, MCPSession


class ToolSerializer(serializers.ModelSerializer):
    """
    Serializzatore per Tool MCP.
    
    Converte i tool nel formato richiesto dal protocollo MCP.
    Il formato segue le specifiche per tool discovery e listing.
    
    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/tools/
    """
    
    # Rinomina input_schema a inputSchema per compatibilità MCP
    inputSchema = serializers.JSONField(source='input_schema')
    
    class Meta:
        model = Tool
        fields = ['name', 'description', 'inputSchema']
    
    def to_representation(self, instance):
        """
        Personalizza la rappresentazione per seguire il formato MCP.
        
        Il protocollo MCP richiede un formato specifico per i tools
        che include name, description e inputSchema.
        """
        return {
            "name": instance.name,
            "description": instance.description,
            "inputSchema": instance.input_schema
        }


class MCPInitializeRequestSerializer(serializers.Serializer):
    """
    Serializzatore per la richiesta di inizializzazione MCP.
    
    Valida i dati inviati dal client durante l'handshake iniziale.
    Include informazioni sul client e le sue capacità.
    
    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/lifecycle/
    """
    
    # Informazioni sul client (obbligatorie)
    clientInfo = serializers.DictField(
        help_text="Informazioni sul client MCP (name, version)"
    )
    
    # Capacità del client (opzionali)
    capabilities = serializers.DictField(
        required=False,
        default=dict,
        help_text="Capacità supportate dal client"
    )
    
    def validate_clientInfo(self, value):
        """
        Valida le informazioni del client.
        
        Il protocollo MCP richiede almeno name e version.
        """
        if 'name' not in value:
            raise serializers.ValidationError("clientInfo deve contenere 'name'")
        if 'version' not in value:
            raise serializers.ValidationError("clientInfo deve contenere 'version'")
        return value


class MCPInitializeResponseSerializer(serializers.Serializer):
    """
    Serializzatore per la risposta di inizializzazione MCP.
    
    Formato della risposta del server durante l'handshake.
    Include informazioni sul server e capacità supportate.
    """
    
    # Informazioni sul server
    serverInfo = serializers.DictField(
        help_text="Informazioni sul server MCP"
    )
    
    # Capacità del server
    capabilities = serializers.DictField(
        help_text="Capacità supportate dal server"
    )
    
    # ID della sessione creata
    sessionId = serializers.UUIDField(
        help_text="Identificatore univoco della sessione"
    )


class ToolCallRequestSerializer(serializers.Serializer):
    """
    Serializzatore per richieste di chiamata tool.
    
    Valida i parametri per l'esecuzione di un tool MCP.
    Include nome del tool e parametri di input.
    
    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/tools/
    """
    
    # Nome del tool da chiamare
    name = serializers.CharField(
        max_length=100,
        help_text="Nome del tool da eseguire"
    )
    
    # Parametri per il tool
    arguments = serializers.JSONField(
        required=False,
        default=dict,
        help_text="Parametri da passare al tool"
    )
    
    def validate_name(self, value):
        """
        Valida che il tool esista e sia attivo.
        """
        try:
            tool = Tool.objects.get(name=value, is_active=True)
        except Tool.DoesNotExist:
            raise serializers.ValidationError(f"Tool '{value}' non trovato o non attivo")
        return value


class ToolCallResponseSerializer(serializers.Serializer):
    """
    Serializzatore per risposte di chiamata tool.
    
    Formato della risposta dopo l'esecuzione di un tool.
    Include risultato o errore secondo le specifiche MCP.
    """
    
    # ID della chiamata per tracciamento
    callId = serializers.UUIDField(
        help_text="Identificatore univoco della chiamata"
    )
    
    # Risultato dell'esecuzione (se successo)
    content = serializers.JSONField(
        required=False,
        help_text="Risultato dell'esecuzione del tool"
    )
    
    # Informazioni su errore (se fallimento)
    isError = serializers.BooleanField(
        default=False,
        help_text="Se l'esecuzione ha prodotto un errore"
    )
    
    # Messaggio di errore
    error = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Messaggio di errore se isError=true"
    )


class ToolCallSerializer(serializers.ModelSerializer):
    """
    Serializzatore completo per ToolCall (per admin e debugging).
    """
    
    tool_name = serializers.CharField(source='tool.name', read_only=True)
    session_id = serializers.UUIDField(source='session.session_id', read_only=True)
    
    class Meta:
        model = ToolCall
        fields = [
            'call_id', 'tool_name', 'session_id', 'parameters', 
            'result', 'success', 'error_message', 'started_at', 
            'completed_at', 'execution_time_ms'
        ]
        read_only_fields = ['call_id', 'started_at']


class MCPSessionSerializer(serializers.ModelSerializer):
    """
    Serializzatore per MCPSession (per admin e debugging).
    """
    
    class Meta:
        model = MCPSession
        fields = [
            'session_id', 'client_name', 'client_version',
            'client_capabilities', 'server_capabilities',
            'is_active', 'created_at', 'last_activity'
        ]
        read_only_fields = ['session_id', 'created_at']

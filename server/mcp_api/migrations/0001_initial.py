# Generated by Django 4.2.7 on 2025-06-12 20:44

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MCPSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.UUIDField(default=uuid.uuid4, help_text='Identificatore univoco della sessione MCP', unique=True)),
                ('client_name', models.CharField(help_text='Nome del client MCP che si è connesso', max_length=100)),
                ('client_version', models.Char<PERSON>ield(help_text='Versione del client MCP', max_length=50)),
                ('client_capabilities', models.J<PERSON>NField(default=dict, help_text='Capacità supportate dal client')),
                ('server_capabilities', models.J<PERSON><PERSON>ield(default=dict, help_text='Capacità supportate dal server')),
                ('is_active', models.Boolean<PERSON>ield(default=True, help_text='Se la sessione è attiva')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Sessione MCP',
                'verbose_name_plural': 'Sessioni MCP',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Tool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Nome univoco del tool (es. 'calculator', 'file_reader')", max_length=100, unique=True)),
                ('description', models.TextField(help_text='Descrizione di cosa fa il tool e come usarlo')),
                ('input_schema', models.JSONField(help_text='Schema JSON per validare i parametri di input del tool')),
                ('is_active', models.BooleanField(default=True, help_text="Se il tool è attivo e disponibile per l'uso")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Tool MCP',
                'verbose_name_plural': 'Tools MCP',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ToolCall',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('call_id', models.UUIDField(default=uuid.uuid4, help_text='Identificatore univoco della chiamata', unique=True)),
                ('parameters', models.JSONField(help_text='Parametri passati al tool in formato JSON')),
                ('result', models.JSONField(blank=True, help_text="Risultato dell'esecuzione del tool", null=True)),
                ('success', models.BooleanField(default=False, help_text="Se l'esecuzione è stata completata con successo")),
                ('error_message', models.TextField(blank=True, help_text="Messaggio di errore se l'esecuzione è fallita", null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('execution_time_ms', models.IntegerField(blank=True, help_text='Tempo di esecuzione in millisecondi', null=True)),
                ('session', models.ForeignKey(help_text='Sessione MCP in cui è avvenuta la chiamata', on_delete=django.db.models.deletion.CASCADE, related_name='tool_calls', to='mcp_api.mcpsession')),
                ('tool', models.ForeignKey(help_text='Tool che è stato chiamato', on_delete=django.db.models.deletion.CASCADE, related_name='calls', to='mcp_api.tool')),
            ],
            options={
                'verbose_name': 'Chiamata Tool',
                'verbose_name_plural': 'Chiamate Tools',
                'ordering': ['-started_at'],
            },
        ),
    ]

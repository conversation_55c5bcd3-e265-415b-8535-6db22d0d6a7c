"""
Tool Calcolatrice per il protocollo MCP.

Implementa un tool di esempio che esegue operazioni matematiche di base.
Dimostra come implementare un tool MCP con validazione input e gestione errori.

Questo tool segue le specifiche MCP per l'esecuzione di strumenti:
https://spec.modelcontextprotocol.io/specification/basic/tools/
"""

import logging
from typing import Dict, Any, Union
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)


class CalculatorTool:
    """
    Tool MCP per operazioni matematiche di base.
    
    Supporta addizione, sottrazione, moltiplicazione e divisione
    con validazione degli input e gestione degli errori.
    """
    
    @staticmethod
    def get_tool_definition() -> Dict[str, Any]:
        """
        Restituisce la definizione del tool nel formato MCP.
        
        Include nome, descrizione e schema JSON per i parametri.
        Lo schema segue lo standard JSON Schema per validazione.
        
        Returns:
            dict: Definizione del tool in formato MCP
        """
        return {
            "name": "calculator",
            "description": "Esegue operazioni matematiche di base (addizione, sottrazione, moltiplicazione, divisione)",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["add", "subtract", "multiply", "divide"],
                        "description": "Tipo di operazione da eseguire"
                    },
                    "a": {
                        "type": "number",
                        "description": "Primo numero dell'operazione"
                    },
                    "b": {
                        "type": "number",
                        "description": "Secondo numero dell'operazione"
                    }
                },
                "required": ["operation", "a", "b"],
                "additionalProperties": False
            }
        }
    
    @staticmethod
    def execute(parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Esegue l'operazione matematica richiesta.
        
        Args:
            parameters: Parametri dell'operazione (operation, a, b)
            
        Returns:
            dict: Risultato dell'operazione o errore
            
        Raises:
            ValueError: Se i parametri non sono validi
            ZeroDivisionError: Se si tenta di dividere per zero
        """
        try:
            # Estrai parametri
            operation = parameters.get('operation')
            a = parameters.get('a')
            b = parameters.get('b')
            
            # Validazione parametri (doppia validazione per sicurezza)
            if operation not in ['add', 'subtract', 'multiply', 'divide']:
                raise ValueError(f"Operazione non supportata: {operation}")
            
            if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
                raise ValueError("I parametri 'a' e 'b' devono essere numeri")
            
            # Converti a Decimal per precisione matematica
            try:
                num_a = Decimal(str(a))
                num_b = Decimal(str(b))
            except InvalidOperation:
                raise ValueError("Numeri non validi forniti")
            
            # Esegui operazione
            result = None
            operation_symbol = ""
            
            if operation == 'add':
                result = num_a + num_b
                operation_symbol = "+"
            elif operation == 'subtract':
                result = num_a - num_b
                operation_symbol = "-"
            elif operation == 'multiply':
                result = num_a * num_b
                operation_symbol = "*"
            elif operation == 'divide':
                if num_b == 0:
                    raise ZeroDivisionError("Impossibile dividere per zero")
                result = num_a / num_b
                operation_symbol = "/"
            
            # Converti risultato a float per JSON serialization
            result_float = float(result)
            
            # Log dell'operazione per debugging
            logger.info(f"Calcolatrice: {a} {operation_symbol} {b} = {result_float}")
            
            # Restituisci risultato in formato MCP
            return {
                "success": True,
                "result": result_float,
                "operation": f"{a} {operation_symbol} {b} = {result_float}",
                "details": {
                    "operation_type": operation,
                    "operand_a": a,
                    "operand_b": b,
                    "result": result_float
                }
            }
            
        except ZeroDivisionError as e:
            logger.warning(f"Errore divisione per zero: {e}")
            return {
                "success": False,
                "error": "Errore: Divisione per zero non permessa",
                "error_type": "ZeroDivisionError",
                "details": str(e)
            }
            
        except ValueError as e:
            logger.warning(f"Errore validazione parametri: {e}")
            return {
                "success": False,
                "error": f"Errore nei parametri: {str(e)}",
                "error_type": "ValueError",
                "details": str(e)
            }
            
        except Exception as e:
            logger.error(f"Errore inaspettato nella calcolatrice: {e}")
            return {
                "success": False,
                "error": "Errore interno del tool",
                "error_type": type(e).__name__,
                "details": str(e)
            }


# Funzioni di utilità per registrazione del tool

def register_calculator_tool():
    """
    Registra il tool calcolatrice nel database.
    
    Questa funzione viene chiamata durante l'inizializzazione
    per assicurarsi che il tool sia disponibile.
    """
    from ..models import Tool
    
    tool_def = CalculatorTool.get_tool_definition()
    
    # Crea o aggiorna il tool nel database
    tool, created = Tool.objects.get_or_create(
        name=tool_def['name'],
        defaults={
            'description': tool_def['description'],
            'input_schema': tool_def['inputSchema'],
            'is_active': True
        }
    )
    
    if not created:
        # Aggiorna tool esistente
        tool.description = tool_def['description']
        tool.input_schema = tool_def['inputSchema']
        tool.is_active = True
        tool.save()
    
    logger.info(f"Tool calcolatrice {'creato' if created else 'aggiornato'}")
    return tool


def get_available_tools():
    """
    Restituisce tutti i tools disponibili.
    
    Returns:
        list: Lista di definizioni tools in formato MCP
    """
    return [
        CalculatorTool.get_tool_definition(),
        # Qui si possono aggiungere altri tools
    ]

"""
Modelli Django per il protocollo MCP.

Questi modelli rappresentano le entità principali del protocollo MCP:
- Tool: Strumenti disponibili per l'esecuzione
- ToolCall: Chiamate a strumenti con parametri e risultati
- MCPSession: Sessioni di connessione MCP

Documentazione MCP: https://modelcontextprotocol.io/
Specifiche: https://spec.modelcontextprotocol.io/
"""

from django.db import models
from django.contrib.auth.models import User
import json
import uuid


class Tool(models.Model):
    """
    Modello per rappresentare un Tool MCP.
    
    Nel protocollo MCP, i tools sono funzioni che il server espone
    e che i client possono chiamare. Ogni tool ha un nome, descrizione
    e schema per i parametri di input.
    
    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/tools/
    """
    
    # Identificatore univoco del tool
    name = models.CharField(
        max_length=100, 
        unique=True,
        help_text="Nome univoco del tool (es. 'calculator', 'file_reader')"
    )
    
    # Descrizione human-readable del tool
    description = models.TextField(
        help_text="Descrizione di cosa fa il tool e come usarlo"
    )
    
    # Schema JSON per validare i parametri di input
    # Segue lo standard JSON Schema: https://json-schema.org/
    input_schema = models.JSONField(
        help_text="Schema JSON per validare i parametri di input del tool"
    )
    
    # Metadati aggiuntivi del tool
    is_active = models.BooleanField(
        default=True,
        help_text="Se il tool è attivo e disponibile per l'uso"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Tool MCP"
        verbose_name_plural = "Tools MCP"
        ordering = ['name']
    
    def __str__(self):
        return f"Tool: {self.name}"
    
    def to_mcp_format(self):
        """
        Converte il tool nel formato richiesto dal protocollo MCP.
        
        Returns:
            dict: Tool nel formato MCP standard
        """
        return {
            "name": self.name,
            "description": self.description,
            "inputSchema": self.input_schema
        }


class MCPSession(models.Model):
    """
    Modello per rappresentare una sessione MCP.
    
    Una sessione MCP rappresenta una connessione attiva tra client e server.
    Include informazioni sulla negoziazione delle capacità e stato della connessione.
    
    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/lifecycle/
    """
    
    # ID univoco della sessione
    session_id = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        help_text="Identificatore univoco della sessione MCP"
    )
    
    # Informazioni sul client
    client_name = models.CharField(
        max_length=100,
        help_text="Nome del client MCP che si è connesso"
    )
    
    client_version = models.CharField(
        max_length=50,
        help_text="Versione del client MCP"
    )
    
    # Capacità negoziate durante l'handshake
    client_capabilities = models.JSONField(
        default=dict,
        help_text="Capacità supportate dal client"
    )
    
    server_capabilities = models.JSONField(
        default=dict,
        help_text="Capacità supportate dal server"
    )
    
    # Stato della sessione
    is_active = models.BooleanField(
        default=True,
        help_text="Se la sessione è attiva"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Sessione MCP"
        verbose_name_plural = "Sessioni MCP"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Sessione {self.client_name} - {self.session_id}"


class ToolCall(models.Model):
    """
    Modello per rappresentare una chiamata a un Tool MCP.
    
    Registra ogni chiamata a un tool con parametri, risultato e metadati.
    Utile per logging, debugging e analytics.
    
    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/tools/
    """
    
    # Riferimento al tool chiamato
    tool = models.ForeignKey(
        Tool,
        on_delete=models.CASCADE,
        related_name='calls',
        help_text="Tool che è stato chiamato"
    )
    
    # Riferimento alla sessione MCP
    session = models.ForeignKey(
        MCPSession,
        on_delete=models.CASCADE,
        related_name='tool_calls',
        help_text="Sessione MCP in cui è avvenuta la chiamata"
    )
    
    # ID univoco della chiamata (per tracciamento)
    call_id = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        help_text="Identificatore univoco della chiamata"
    )
    
    # Parametri passati al tool
    parameters = models.JSONField(
        help_text="Parametri passati al tool in formato JSON"
    )
    
    # Risultato dell'esecuzione
    result = models.JSONField(
        null=True,
        blank=True,
        help_text="Risultato dell'esecuzione del tool"
    )
    
    # Informazioni sull'esecuzione
    success = models.BooleanField(
        default=False,
        help_text="Se l'esecuzione è stata completata con successo"
    )
    
    error_message = models.TextField(
        null=True,
        blank=True,
        help_text="Messaggio di errore se l'esecuzione è fallita"
    )
    
    # Metadati temporali
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Durata esecuzione in millisecondi
    execution_time_ms = models.IntegerField(
        null=True,
        blank=True,
        help_text="Tempo di esecuzione in millisecondi"
    )
    
    class Meta:
        verbose_name = "Chiamata Tool"
        verbose_name_plural = "Chiamate Tools"
        ordering = ['-started_at']
    
    def __str__(self):
        status = "✓" if self.success else "✗"
        return f"{status} {self.tool.name} - {self.call_id}"

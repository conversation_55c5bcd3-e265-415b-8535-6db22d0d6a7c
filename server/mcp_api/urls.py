"""
URL routing per l'app MCP API.

Definisce tutti gli endpoint del protocollo MCP:
- Inizializzazione e handshake
- Gestione tools (listing e esecuzione)
- Server-Sent Events per comunicazione real-time
- Informazioni server e debugging

Documentazione MCP: https://modelcontextprotocol.io/
"""

from django.urls import path
from . import views

# Namespace per l'app MCP
app_name = 'mcp_api'

urlpatterns = [
    # Endpoint di inizializzazione MCP
    # POST /api/mcp/initialize/ - Handshake iniziale client-server
    path('initialize/', views.initialize_mcp, name='initialize'),
    
    # Endpoint per gestione tools
    # GET /api/mcp/tools/ - Lista tools disponibili
    path('tools/', views.list_tools, name='list_tools'),
    
    # POST /api/mcp/tools/call/ - Esecuzione tool
    path('tools/call/', views.call_tool, name='call_tool'),
    
    # Endpoint per Server-Sent Events
    # GET /api/mcp/events/ - Stream eventi real-time
    path('events/', views.mcp_events_stream, name='events_stream'),
    
    # Endpoint informazioni server
    # GET /api/mcp/info/ - Metadati e statistiche server
    path('info/', views.server_info, name='server_info'),
]

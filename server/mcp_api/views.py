"""
Viste Django REST Framework per il protocollo MCP.

Implementa gli endpoint principali del protocollo MCP:
- Inizializzazione e handshake
- Listing e chiamata tools
- Server-Sent Events per comunicazione real-time

Documentazione MCP: https://modelcontextprotocol.io/
Specifiche: https://spec.modelcontextprotocol.io/
"""

import logging
import json
import time
from datetime import datetime
from typing import Dict, Any

from django.http import StreamingHttpResponse, JsonResponse
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from django_eventstream import send_event

from .models import Tool, ToolCall, MCPSession
from .serializers import (
    ToolSerializer, 
    MCPInitializeRequestSerializer,
    MCPInitializeResponseSerializer,
    ToolCallRequestSerializer,
    ToolCallResponseSerializer
)
from .tools.calculator import CalculatorTool, register_calculator_tool

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([AllowAny])
def initialize_mcp(request):
    """
    Endpoint per inizializzazione del protocollo MCP.
    
    Implementa l'handshake iniziale tra client e server secondo
    le specifiche MCP. Negozia le capacità e crea una sessione.
    
    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/lifecycle/
    
    POST /api/mcp/initialize/
    Body: {
        "clientInfo": {"name": "client_name", "version": "1.0.0"},
        "capabilities": {...}
    }
    
    Returns:
        dict: Informazioni server, capacità e session ID
    """
    logger.info("Ricevuta richiesta di inizializzazione MCP")
    
    # Valida richiesta client
    serializer = MCPInitializeRequestSerializer(data=request.data)
    if not serializer.is_valid():
        logger.warning(f"Richiesta inizializzazione non valida: {serializer.errors}")
        return Response({
            "error": "Richiesta non valida",
            "details": serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    validated_data = serializer.validated_data
    client_info = validated_data['clientInfo']
    client_capabilities = validated_data.get('capabilities', {})
    
    # Registra tools disponibili nel database
    register_calculator_tool()
    
    # Definisci capacità del server MCP
    server_capabilities = {
        "tools": {
            "listChanged": True,  # Server può notificare cambi nella lista tools
            "supportsProgress": False  # Non supportiamo progress reporting per ora
        },
        "resources": {
            "subscribe": False,  # Non supportiamo subscription a risorse
            "listChanged": False
        },
        "prompts": {
            "listChanged": False  # Non supportiamo prompts per ora
        },
        "logging": {
            "level": "info"  # Livello di logging supportato
        }
    }
    
    # Crea sessione MCP
    try:
        session = MCPSession.objects.create(
            client_name=client_info['name'],
            client_version=client_info['version'],
            client_capabilities=client_capabilities,
            server_capabilities=server_capabilities,
            is_active=True
        )
        
        logger.info(f"Creata sessione MCP: {session.session_id} per client {client_info['name']}")
        
        # Prepara risposta secondo formato MCP
        response_data = {
            "serverInfo": {
                "name": "MCP Learning Server",
                "version": "1.0.0",
                "description": "Server MCP educativo con Django e Docker"
            },
            "capabilities": server_capabilities,
            "sessionId": str(session.session_id)
        }
        
        # Invia evento di nuova sessione via SSE
        send_event('mcp_events', 'session_created', {
            'session_id': str(session.session_id),
            'client_name': client_info['name'],
            'timestamp': timezone.now().isoformat()
        })
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Errore durante creazione sessione: {e}")
        return Response({
            "error": "Errore interno del server",
            "details": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def list_tools(request):
    """
    Endpoint per ottenere la lista dei tools disponibili.

    Implementa il tool discovery del protocollo MCP.
    Restituisce tutti i tools attivi registrati nel server.

    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/tools/

    GET /api/mcp/tools/

    Returns:
        dict: Lista tools in formato MCP
    """
    logger.info("Richiesta lista tools MCP")

    try:
        # Ottieni tutti i tools attivi dal database
        tools = Tool.objects.filter(is_active=True)

        # Serializza in formato MCP
        tools_data = []
        for tool in tools:
            tools_data.append(tool.to_mcp_format())

        response_data = {
            "tools": tools_data,
            "count": len(tools_data)
        }

        logger.info(f"Restituiti {len(tools_data)} tools disponibili")
        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Errore durante recupero tools: {e}")
        return Response({
            "error": "Errore interno del server",
            "details": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def call_tool(request):
    """
    Endpoint per eseguire un tool MCP.

    Implementa l'esecuzione di tools secondo le specifiche MCP.
    Valida parametri, esegue il tool e registra la chiamata.

    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/tools/

    POST /api/mcp/tools/call/
    Body: {
        "name": "tool_name",
        "arguments": {...}
    }

    Returns:
        dict: Risultato esecuzione tool
    """
    logger.info("Ricevuta richiesta di esecuzione tool")

    # Valida richiesta
    serializer = ToolCallRequestSerializer(data=request.data)
    if not serializer.is_valid():
        logger.warning(f"Richiesta tool call non valida: {serializer.errors}")
        return Response({
            "error": "Richiesta non valida",
            "details": serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    validated_data = serializer.validated_data
    tool_name = validated_data['name']
    arguments = validated_data.get('arguments', {})

    # Ottieni session ID dall'header (opzionale per questo esempio)
    session_id = request.headers.get('X-MCP-Session-ID')
    session = None

    if session_id:
        try:
            session = MCPSession.objects.get(session_id=session_id, is_active=True)
        except MCPSession.DoesNotExist:
            logger.warning(f"Sessione non trovata: {session_id}")

    try:
        # Ottieni il tool dal database
        tool = Tool.objects.get(name=tool_name, is_active=True)

        # Crea record della chiamata
        tool_call = ToolCall.objects.create(
            tool=tool,
            session=session,
            parameters=arguments
        )

        start_time = time.time()

        # Esegui il tool specifico
        if tool_name == 'calculator':
            result = CalculatorTool.execute(arguments)
        else:
            # Placeholder per altri tools
            result = {
                "success": False,
                "error": f"Tool '{tool_name}' non implementato",
                "error_type": "NotImplementedError"
            }

        # Calcola tempo di esecuzione
        execution_time = int((time.time() - start_time) * 1000)  # millisecondi

        # Aggiorna record della chiamata
        tool_call.result = result
        tool_call.success = result.get('success', False)
        tool_call.error_message = result.get('error', '')
        tool_call.completed_at = timezone.now()
        tool_call.execution_time_ms = execution_time
        tool_call.save()

        # Prepara risposta in formato MCP
        response_data = {
            "callId": str(tool_call.call_id),
            "content": result if result.get('success') else None,
            "isError": not result.get('success', False),
            "error": result.get('error', '') if not result.get('success') else None
        }

        # Invia evento via SSE
        send_event('mcp_events', 'tool_executed', {
            'tool_name': tool_name,
            'call_id': str(tool_call.call_id),
            'success': result.get('success', False),
            'execution_time_ms': execution_time,
            'timestamp': timezone.now().isoformat()
        })

        logger.info(f"Tool '{tool_name}' eseguito con successo: {tool_call.call_id}")
        return Response(response_data, status=status.HTTP_200_OK)

    except Tool.DoesNotExist:
        logger.warning(f"Tool non trovato: {tool_name}")
        return Response({
            "error": f"Tool '{tool_name}' non trovato",
            "isError": True
        }, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"Errore durante esecuzione tool: {e}")
        return Response({
            "error": "Errore interno del server",
            "details": str(e),
            "isError": True
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@require_http_methods(["GET"])
def mcp_events_stream(request):
    """
    Endpoint per Server-Sent Events (SSE) del protocollo MCP.

    Fornisce un stream di eventi real-time per comunicazione asincrona
    tra server e client. Include eventi di sessione, tool execution, ecc.

    Riferimento: https://spec.modelcontextprotocol.io/specification/basic/transports/

    GET /api/mcp/events/
    Headers: Accept: text/event-stream

    Returns:
        StreamingHttpResponse: Stream SSE di eventi MCP
    """
    logger.info("Client connesso al stream eventi MCP")

    def event_generator():
        """
        Generatore di eventi SSE per il protocollo MCP.

        Invia eventi di heartbeat e notifiche di sistema.
        Gli eventi specifici vengono inviati tramite django-eventstream.
        """
        # Evento di connessione iniziale
        yield f"data: {json.dumps({'type': 'connected', 'timestamp': timezone.now().isoformat()})}\n\n"

        # Heartbeat ogni 30 secondi per mantenere connessione
        while True:
            try:
                heartbeat_data = {
                    'type': 'heartbeat',
                    'timestamp': timezone.now().isoformat(),
                    'server_status': 'active'
                }
                yield f"data: {json.dumps(heartbeat_data)}\n\n"
                time.sleep(30)

            except Exception as e:
                logger.error(f"Errore nel stream eventi: {e}")
                break

    # Crea risposta streaming con headers SSE
    response = StreamingHttpResponse(
        event_generator(),
        content_type='text/event-stream'
    )

    # Headers necessari per SSE
    response['Cache-Control'] = 'no-cache'
    response['Connection'] = 'keep-alive'
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Headers'] = 'Cache-Control'

    return response


@api_view(['GET'])
@permission_classes([AllowAny])
def server_info(request):
    """
    Endpoint per informazioni sul server MCP.

    Fornisce metadati sul server, statistiche e stato.
    Utile per monitoring e debugging.

    GET /api/mcp/info/

    Returns:
        dict: Informazioni server e statistiche
    """
    try:
        # Statistiche dal database
        total_sessions = MCPSession.objects.count()
        active_sessions = MCPSession.objects.filter(is_active=True).count()
        total_tools = Tool.objects.filter(is_active=True).count()
        total_tool_calls = ToolCall.objects.count()
        successful_calls = ToolCall.objects.filter(success=True).count()

        # Calcola success rate
        success_rate = (successful_calls / total_tool_calls * 100) if total_tool_calls > 0 else 0

        server_info = {
            "name": "MCP Learning Server",
            "version": "1.0.0",
            "description": "Server MCP educativo con Django e Docker",
            "protocol_version": "1.0.0",
            "status": "active",
            "uptime": timezone.now().isoformat(),
            "statistics": {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "available_tools": total_tools,
                "total_tool_calls": total_tool_calls,
                "successful_calls": successful_calls,
                "success_rate_percent": round(success_rate, 2)
            },
            "capabilities": {
                "tools": True,
                "resources": False,
                "prompts": False,
                "logging": True,
                "events": True
            }
        }

        return Response(server_info, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Errore durante recupero info server: {e}")
        return Response({
            "error": "Errore interno del server",
            "details": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

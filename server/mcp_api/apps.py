"""
Configurazione app Django per MCP API.

Questa app implementa il protocollo Model Context Protocol (MCP)
fornendo endpoint RESTful per inizializzazione, gestione tools e eventi.
"""

from django.apps import AppConfig


class McpApiConfig(AppConfig):
    """
    Configurazione per l'app MCP API.
    
    Implementa il protocollo MCP secondo le specifiche ufficiali:
    https://spec.modelcontextprotocol.io/
    """
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'mcp_api'
    verbose_name = 'MCP API - Model Context Protocol'

"""
Configurazione Django Admin per modelli MCP.

Fornisce interfaccia web per gestire tools, sessioni e chiamate.
Utile per debugging e monitoraggio del protocollo MCP.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import Tool, MCPSession, ToolCall


@admin.register(Tool)
class ToolAdmin(admin.ModelAdmin):
    """
    Admin per gestione Tools MCP.
    """
    list_display = ['name', 'description_short', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Informazioni Tool', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Schema Input', {
            'fields': ('input_schema',),
            'classes': ('collapse',)
        }),
        ('Metadati', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def description_short(self, obj):
        """Descrizione abbreviata per lista."""
        return obj.description[:50] + "..." if len(obj.description) > 50 else obj.description
    description_short.short_description = "Descrizione"


@admin.register(MCPSession)
class MCPSessionAdmin(admin.ModelAdmin):
    """
    Admin per gestione Sessioni MCP.
    """
    list_display = ['session_id_short', 'client_name', 'client_version', 'is_active', 'created_at', 'tool_calls_count']
    list_filter = ['is_active', 'client_name', 'created_at']
    search_fields = ['client_name', 'session_id']
    readonly_fields = ['session_id', 'created_at', 'last_activity']
    
    fieldsets = (
        ('Informazioni Sessione', {
            'fields': ('session_id', 'client_name', 'client_version', 'is_active')
        }),
        ('Capacità', {
            'fields': ('client_capabilities', 'server_capabilities'),
            'classes': ('collapse',)
        }),
        ('Metadati', {
            'fields': ('created_at', 'last_activity'),
            'classes': ('collapse',)
        }),
    )
    
    def session_id_short(self, obj):
        """ID sessione abbreviato per lista."""
        return str(obj.session_id)[:8] + "..."
    session_id_short.short_description = "Session ID"
    
    def tool_calls_count(self, obj):
        """Numero di chiamate tools per questa sessione."""
        return obj.tool_calls.count()
    tool_calls_count.short_description = "Tool Calls"


@admin.register(ToolCall)
class ToolCallAdmin(admin.ModelAdmin):
    """
    Admin per gestione Chiamate Tools MCP.
    """
    list_display = ['call_id_short', 'tool_name', 'success_icon', 'execution_time_ms', 'started_at']
    list_filter = ['success', 'tool__name', 'started_at']
    search_fields = ['call_id', 'tool__name']
    readonly_fields = ['call_id', 'started_at', 'completed_at']
    
    fieldsets = (
        ('Informazioni Chiamata', {
            'fields': ('call_id', 'tool', 'session', 'success')
        }),
        ('Parametri e Risultato', {
            'fields': ('parameters', 'result', 'error_message'),
            'classes': ('collapse',)
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at', 'execution_time_ms'),
            'classes': ('collapse',)
        }),
    )
    
    def call_id_short(self, obj):
        """ID chiamata abbreviato per lista."""
        return str(obj.call_id)[:8] + "..."
    call_id_short.short_description = "Call ID"
    
    def tool_name(self, obj):
        """Nome del tool chiamato."""
        return obj.tool.name
    tool_name.short_description = "Tool"
    
    def success_icon(self, obj):
        """Icona per indicare successo/fallimento."""
        if obj.success:
            return format_html('<span style="color: green;">✓</span>')
        else:
            return format_html('<span style="color: red;">✗</span>')
    success_icon.short_description = "Successo"

"""
Configurazione Django Admin per modelli MCP.

Fornisce interfaccia web per gestire tools, sessioni e chiamate.
Utile per debugging e monitoraggio del protocollo MCP.

Include widget personalizzati per JSONField con syntax highlighting
e validazione in tempo reale.
"""

from django.contrib import admin
from django.utils.html import format_html
from django import forms
from .models import Tool, MCPSession, ToolCall
from .widgets import PrettyJSONWidget, CompactJSONWidget


class ToolAdminForm(forms.ModelForm):
    """
    Form personalizzato per Tool con widget JSON avanzato.
    """
    class Meta:
        model = Tool
        fields = '__all__'
        widgets = {
            'input_schema': PrettyJSONWidget(attrs={
                'rows': 15,
                'placeholder': '{\n  "type": "object",\n  "properties": {\n    "param1": {\n      "type": "string",\n      "description": "Descrizione parametro"\n    }\n  },\n  "required": ["param1"]\n}'
            }),
        }


class MCPSessionAdminForm(forms.ModelForm):
    """
    Form personalizzato per MCPSession con widget JSON per capacità.
    """
    class Meta:
        model = MCPSession
        fields = '__all__'
        widgets = {
            'client_capabilities': PrettyJSONWidget(attrs={
                'rows': 10,
                'placeholder': '{\n  "tools": {\n    "supportsProgress": false\n  }\n}'
            }),
            'server_capabilities': PrettyJSONWidget(attrs={
                'rows': 10,
                'placeholder': '{\n  "tools": {\n    "listChanged": true\n  }\n}'
            }),
        }


class ToolCallAdminForm(forms.ModelForm):
    """
    Form personalizzato per ToolCall con widget JSON per parametri e risultati.
    """
    class Meta:
        model = ToolCall
        fields = '__all__'
        widgets = {
            'parameters': PrettyJSONWidget(attrs={
                'rows': 8,
                'placeholder': '{\n  "operation": "add",\n  "a": 10,\n  "b": 5\n}'
            }),
            'result': PrettyJSONWidget(attrs={
                'rows': 12,
                'placeholder': '{\n  "success": true,\n  "result": 15.0\n}'
            }),
        }


@admin.register(Tool)
class ToolAdmin(admin.ModelAdmin):
    """
    Admin per gestione Tools MCP con widget JSON avanzato.
    """
    form = ToolAdminForm
    list_display = ['name', 'description_short', 'is_active', 'schema_preview', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Informazioni Tool', {
            'fields': ('name', 'description', 'is_active'),
            'description': 'Informazioni di base del tool MCP'
        }),
        ('Schema Input JSON', {
            'fields': ('input_schema',),
            'description': 'Schema JSON per validare i parametri di input del tool. Usa il widget avanzato con syntax highlighting e validazione.'
        }),
        ('Metadati', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def description_short(self, obj):
        """Descrizione abbreviata per lista."""
        return obj.description[:50] + "..." if len(obj.description) > 50 else obj.description
    description_short.short_description = "Descrizione"

    def schema_preview(self, obj):
        """Anteprima dello schema JSON."""
        if not obj.input_schema:
            return format_html('<span style="color: #999;">Nessuno schema</span>')

        # Mostra il tipo principale e numero di proprietà
        schema_type = obj.input_schema.get('type', 'unknown')
        properties = obj.input_schema.get('properties', {})
        prop_count = len(properties)

        if schema_type == 'object' and prop_count > 0:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">📋 {} ({} proprietà)</span>',
                schema_type.title(),
                prop_count
            )
        else:
            return format_html(
                '<span style="color: #17a2b8;">📋 {}</span>',
                schema_type.title()
            )
    schema_preview.short_description = "Schema"


@admin.register(MCPSession)
class MCPSessionAdmin(admin.ModelAdmin):
    """
    Admin per gestione Sessioni MCP con widget JSON per capacità.
    """
    form = MCPSessionAdminForm
    list_display = ['session_id_short', 'client_name', 'client_version', 'is_active', 'capabilities_preview', 'created_at', 'tool_calls_count']
    list_filter = ['is_active', 'client_name', 'created_at']
    search_fields = ['client_name', 'session_id']
    readonly_fields = ['session_id', 'created_at', 'last_activity']
    
    fieldsets = (
        ('Informazioni Sessione', {
            'fields': ('session_id', 'client_name', 'client_version', 'is_active'),
            'description': 'Informazioni di base della sessione MCP'
        }),
        ('Capacità Client', {
            'fields': ('client_capabilities',),
            'description': 'Capacità supportate dal client MCP (JSON con syntax highlighting)'
        }),
        ('Capacità Server', {
            'fields': ('server_capabilities',),
            'description': 'Capacità supportate dal server MCP (JSON con syntax highlighting)'
        }),
        ('Metadati', {
            'fields': ('created_at', 'last_activity'),
            'classes': ('collapse',)
        }),
    )
    
    def session_id_short(self, obj):
        """ID sessione abbreviato per lista."""
        return str(obj.session_id)[:8] + "..."
    session_id_short.short_description = "Session ID"
    
    def tool_calls_count(self, obj):
        """Numero di chiamate tools per questa sessione."""
        count = obj.tool_calls.count()
        if count > 0:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">📞 {}</span>',
                count
            )
        else:
            return format_html('<span style="color: #999;">📞 0</span>')
    tool_calls_count.short_description = "Tool Calls"

    def capabilities_preview(self, obj):
        """Anteprima delle capacità negoziate."""
        client_caps = obj.client_capabilities or {}
        server_caps = obj.server_capabilities or {}

        client_tools = client_caps.get('tools', {})
        server_tools = server_caps.get('tools', {})

        # Conta le capacità attive
        client_count = len([k for k, v in client_tools.items() if v])
        server_count = len([k for k, v in server_tools.items() if v])

        return format_html(
            '<span style="color: #17a2b8;">🔧 Client: {} | Server: {}</span>',
            client_count,
            server_count
        )
    capabilities_preview.short_description = "Capacità"


@admin.register(ToolCall)
class ToolCallAdmin(admin.ModelAdmin):
    """
    Admin per gestione Chiamate Tools MCP con widget JSON per parametri e risultati.
    """
    form = ToolCallAdminForm
    list_display = ['call_id_short', 'tool_name', 'success_icon', 'params_preview', 'execution_time_ms', 'started_at']
    list_filter = ['success', 'tool__name', 'started_at']
    search_fields = ['call_id', 'tool__name']
    readonly_fields = ['call_id', 'started_at', 'completed_at']
    
    fieldsets = (
        ('Informazioni Chiamata', {
            'fields': ('call_id', 'tool', 'session', 'success'),
            'description': 'Informazioni di base della chiamata al tool MCP'
        }),
        ('Parametri Input (JSON)', {
            'fields': ('parameters',),
            'description': 'Parametri passati al tool in formato JSON con syntax highlighting'
        }),
        ('Risultato Output (JSON)', {
            'fields': ('result',),
            'description': 'Risultato dell\'esecuzione del tool in formato JSON con syntax highlighting'
        }),
        ('Gestione Errori', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Timing e Performance', {
            'fields': ('started_at', 'completed_at', 'execution_time_ms'),
            'classes': ('collapse',)
        }),
    )
    
    def call_id_short(self, obj):
        """ID chiamata abbreviato per lista."""
        return str(obj.call_id)[:8] + "..."
    call_id_short.short_description = "Call ID"
    
    def tool_name(self, obj):
        """Nome del tool chiamato."""
        return obj.tool.name
    tool_name.short_description = "Tool"
    
    def success_icon(self, obj):
        """Icona per indicare successo/fallimento."""
        if obj.success:
            return format_html('<span style="color: #28a745; font-size: 16px;">✅</span>')
        else:
            return format_html('<span style="color: #dc3545; font-size: 16px;">❌</span>')
    success_icon.short_description = "Successo"

    def params_preview(self, obj):
        """Anteprima dei parametri della chiamata."""
        if not obj.parameters:
            return format_html('<span style="color: #999;">Nessun parametro</span>')

        # Mostra i primi parametri
        params = obj.parameters
        if isinstance(params, dict):
            param_count = len(params)
            if param_count > 0:
                # Mostra il primo parametro come esempio
                first_key = list(params.keys())[0]
                first_value = params[first_key]

                # Tronca il valore se troppo lungo
                if isinstance(first_value, str) and len(first_value) > 20:
                    first_value = first_value[:20] + "..."

                return format_html(
                    '<span style="color: #17a2b8;">📝 {} parametri<br><small>{}: {}</small></span>',
                    param_count,
                    first_key,
                    first_value
                )
            else:
                return format_html('<span style="color: #999;">📝 Oggetto vuoto</span>')
        else:
            return format_html('<span style="color: #ffc107;">📝 Non oggetto</span>')
    params_preview.short_description = "Parametri"

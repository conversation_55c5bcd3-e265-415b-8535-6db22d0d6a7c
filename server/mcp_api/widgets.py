"""
Widget personalizzati per Django Admin MCP.

Fornisce widget avanzati per la gestione di JSONField con:
- Syntax highlighting
- Validazione JSON in tempo reale
- Formattazione automatica
- Editor migliorato

Documentazione Django Widgets: https://docs.djangoproject.com/en/4.2/ref/forms/widgets/
"""

import json
from django import forms
from django.utils.safestring import mark_safe
from django.utils.html import format_html


class PrettyJSONWidget(forms.Textarea):
    """
    Widget personalizzato per JSONField con formattazione e syntax highlighting.
    
    Fornisce un'interfaccia migliorata per editare JSON nell'admin Django
    con validazione in tempo reale e formattazione automatica.
    """
    
    def __init__(self, attrs=None):
        """
        Inizializza il widget con attributi personalizzati.
        
        Args:
            attrs: Attributi HTML aggiuntivi
        """
        default_attrs = {
            'class': 'vLargeTextField json-editor',
            'rows': 20,
            'cols': 80,
            'style': 'font-family: monospace; font-size: 12px;'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)
    
    def format_value(self, value):
        """
        Formatta il valore JSON per la visualizzazione.
        
        Args:
            value: Valore del campo (dict, list, str)
            
        Returns:
            str: JSON formattato per l'editor
        """
        if value is None:
            return ''
        
        if isinstance(value, str):
            try:
                # Se è già una stringa JSON, prova a parsarla e riformattarla
                parsed = json.loads(value)
                return json.dumps(parsed, indent=2, ensure_ascii=False, sort_keys=True)
            except (json.JSONDecodeError, TypeError):
                # Se non è JSON valido, restituisci così com'è
                return value
        
        # Se è un dict o list, formattalo come JSON
        try:
            return json.dumps(value, indent=2, ensure_ascii=False, sort_keys=True)
        except (TypeError, ValueError):
            return str(value)
    
    def render(self, name, value, attrs=None, renderer=None):
        """
        Renderizza il widget con JavaScript per validazione e formattazione.
        
        Args:
            name: Nome del campo
            value: Valore del campo
            attrs: Attributi HTML
            renderer: Renderer Django
            
        Returns:
            str: HTML del widget
        """
        # Formatta il valore JSON
        formatted_value = self.format_value(value)
        
        # Renderizza il textarea base
        textarea_html = super().render(name, formatted_value, attrs, renderer)
        
        # JavaScript per validazione e formattazione
        js_code = format_html("""
        <script>
        (function() {{
            const textarea = document.querySelector('textarea[name="{name}"]');
            if (!textarea) return;
            
            // Container per messaggi di errore
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = 'color: red; font-weight: bold; margin-top: 5px; display: none;';
            textarea.parentNode.insertBefore(errorDiv, textarea.nextSibling);
            
            // Container per informazioni
            const infoDiv = document.createElement('div');
            infoDiv.style.cssText = 'color: #666; font-size: 11px; margin-top: 5px;';
            infoDiv.innerHTML = '💡 Suggerimenti: Ctrl+A per selezionare tutto, Tab per indentare';
            textarea.parentNode.insertBefore(infoDiv, errorDiv);
            
            // Pulsante per formattare JSON
            const formatBtn = document.createElement('button');
            formatBtn.type = 'button';
            formatBtn.innerHTML = '🎨 Formatta JSON';
            formatBtn.style.cssText = 'margin-top: 5px; margin-right: 10px; padding: 5px 10px; background: #417690; color: white; border: none; border-radius: 3px; cursor: pointer;';
            textarea.parentNode.insertBefore(formatBtn, infoDiv);
            
            // Pulsante per validare JSON
            const validateBtn = document.createElement('button');
            validateBtn.type = 'button';
            validateBtn.innerHTML = '✅ Valida JSON';
            validateBtn.style.cssText = 'margin-top: 5px; margin-right: 10px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;';
            textarea.parentNode.insertBefore(validateBtn, infoDiv);
            
            // Pulsante per minimizzare JSON
            const minifyBtn = document.createElement('button');
            minifyBtn.type = 'button';
            minifyBtn.innerHTML = '📦 Minimizza';
            minifyBtn.style.cssText = 'margin-top: 5px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;';
            textarea.parentNode.insertBefore(minifyBtn, infoDiv);
            
            // Funzione per validare JSON
            function validateJSON(text) {{
                try {{
                    JSON.parse(text);
                    return {{ valid: true, error: null }};
                }} catch (e) {{
                    return {{ valid: false, error: e.message }};
                }}
            }}
            
            // Funzione per mostrare errori
            function showError(message) {{
                errorDiv.textContent = '❌ Errore JSON: ' + message;
                errorDiv.style.display = 'block';
                textarea.style.borderColor = '#dc3545';
            }}
            
            // Funzione per nascondere errori
            function hideError() {{
                errorDiv.style.display = 'none';
                textarea.style.borderColor = '';
            }}
            
            // Event listener per formattazione
            formatBtn.addEventListener('click', function() {{
                const text = textarea.value.trim();
                if (!text) return;
                
                const validation = validateJSON(text);
                if (validation.valid) {{
                    try {{
                        const parsed = JSON.parse(text);
                        textarea.value = JSON.stringify(parsed, null, 2);
                        hideError();
                        // Feedback visivo
                        formatBtn.innerHTML = '✅ Formattato!';
                        setTimeout(() => formatBtn.innerHTML = '🎨 Formatta JSON', 2000);
                    }} catch (e) {{
                        showError(e.message);
                    }}
                }} else {{
                    showError(validation.error);
                }}
            }});
            
            // Event listener per validazione
            validateBtn.addEventListener('click', function() {{
                const text = textarea.value.trim();
                if (!text) {{
                    hideError();
                    validateBtn.innerHTML = '⚠️ Campo vuoto';
                    setTimeout(() => validateBtn.innerHTML = '✅ Valida JSON', 2000);
                    return;
                }}
                
                const validation = validateJSON(text);
                if (validation.valid) {{
                    hideError();
                    validateBtn.innerHTML = '✅ JSON Valido!';
                    setTimeout(() => validateBtn.innerHTML = '✅ Valida JSON', 2000);
                }} else {{
                    showError(validation.error);
                    validateBtn.innerHTML = '❌ Non Valido';
                    setTimeout(() => validateBtn.innerHTML = '✅ Valida JSON', 2000);
                }}
            }});
            
            // Event listener per minimizzazione
            minifyBtn.addEventListener('click', function() {{
                const text = textarea.value.trim();
                if (!text) return;
                
                const validation = validateJSON(text);
                if (validation.valid) {{
                    try {{
                        const parsed = JSON.parse(text);
                        textarea.value = JSON.stringify(parsed);
                        hideError();
                        minifyBtn.innerHTML = '✅ Minimizzato!';
                        setTimeout(() => minifyBtn.innerHTML = '📦 Minimizza', 2000);
                    }} catch (e) {{
                        showError(e.message);
                    }}
                }} else {{
                    showError(validation.error);
                }}
            }});
            
            // Validazione in tempo reale (con debounce)
            let validationTimeout;
            textarea.addEventListener('input', function() {{
                clearTimeout(validationTimeout);
                validationTimeout = setTimeout(function() {{
                    const text = textarea.value.trim();
                    if (text) {{
                        const validation = validateJSON(text);
                        if (!validation.valid) {{
                            showError(validation.error);
                        }} else {{
                            hideError();
                        }}
                    }} else {{
                        hideError();
                    }}
                }}, 1000); // Aspetta 1 secondo dopo l'ultima modifica
            }});
            
            // Supporto per Tab (indentazione)
            textarea.addEventListener('keydown', function(e) {{
                if (e.key === 'Tab') {{
                    e.preventDefault();
                    const start = this.selectionStart;
                    const end = this.selectionEnd;
                    
                    // Inserisci 2 spazi invece di tab
                    this.value = this.value.substring(0, start) + '  ' + this.value.substring(end);
                    this.selectionStart = this.selectionEnd = start + 2;
                }}
            }});
            
            // Validazione iniziale
            const initialText = textarea.value.trim();
            if (initialText) {{
                const validation = validateJSON(initialText);
                if (!validation.valid) {{
                    showError(validation.error);
                }}
            }}
        }})();
        </script>
        """, name=name)
        
        return mark_safe(textarea_html + js_code)


class CompactJSONWidget(forms.TextInput):
    """
    Widget compatto per JSONField semplici.
    
    Utile per JSON piccoli che non necessitano di un editor completo.
    """
    
    def __init__(self, attrs=None):
        default_attrs = {
            'class': 'vTextField json-compact',
            'style': 'font-family: monospace; width: 100%;'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)
    
    def format_value(self, value):
        """Formatta il valore come JSON compatto."""
        if value is None:
            return ''
        
        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                return json.dumps(parsed, ensure_ascii=False, separators=(',', ':'))
            except (json.JSONDecodeError, TypeError):
                return value
        
        try:
            return json.dumps(value, ensure_ascii=False, separators=(',', ':'))
        except (TypeError, ValueError):
            return str(value)

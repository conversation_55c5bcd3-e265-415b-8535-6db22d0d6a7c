/** @license
 * eventsource.js
 * Available under MIT License (MIT)
 * https://github.com/Yaffle/EventSource/
 */
!function(a){"use strict";function b(a,b,d,e,f){this._internal=new c(a,b,d,e,f)}function c(a,b,c,d,e){this.onStartCallback=b,this.onProgressCallback=c,this.onFinishCallback=d,this.thisArg=e,this.xhr=a,this.state=0,this.charOffset=0,this.offset=0,this.url="",this.withCredentials=!1,this.timeout=0}function d(){this._data={}}function e(){this._listeners=new d}function f(a){l(function(){throw a},0)}function g(a){this.type=a,this.target=void 0}function h(a,b){g.call(this,a),this.data=b.data,this.lastEventId=b.lastEventId}function i(a,b){e.call(this),this.onopen=void 0,this.onmessage=void 0,this.onerror=void 0,this.url="",this.readyState=t,this.withCredentials=!1,this._internal=new j(this,a,b)}function j(a,c,d){this.url=c.toString(),this.readyState=t,this.withCredentials=q&&void 0!=d&&Boolean(d.withCredentials),this.es=a,this.initialRetry=E(1e3,0),this.heartbeatTimeout=E(45e3,0),this.lastEventId="",this.retry=this.initialRetry,this.wasActivity=!1;var e=void 0!=d&&void 0!=d.Transport?d.Transport:r,f=new e;this.transport=new b(f,this.onStart,this.onProgress,this.onFinish,this),this.timeout=0,this.currentState=s,this.dataBuffer=[],this.lastEventIdBuffer="",this.eventTypeBuffer="",this.state=x,this.fieldStart=0,this.valueStart=0,this.es.url=this.url,this.es.readyState=this.readyState,this.es.withCredentials=this.withCredentials,this.onTimeout()}function k(){this.CONNECTING=t,this.OPEN=u,this.CLOSED=v}var l=a.setTimeout,m=a.clearTimeout,n=function(){};b.prototype.open=function(a,b){this._internal.open(a,b)},b.prototype.cancel=function(){this._internal.cancel()},c.prototype.onStart=function(){if(1===this.state){this.state=2;var a=0,b="",c=void 0;if("contentType"in this.xhr)a=200,b="OK",c=this.xhr.contentType;else try{a=this.xhr.status,b=this.xhr.statusText,c=this.xhr.getResponseHeader("Content-Type")}catch(d){a=0,b="",c=void 0}void 0==c&&(c=""),this.onStartCallback.call(this.thisArg,a,b,c)}},c.prototype.onProgress=function(){if(this.onStart(),2===this.state||3===this.state){this.state=3;var a="";try{a=this.xhr.responseText}catch(b){}for(var c=this.charOffset,d=a.length,e=this.offset;d>e;e+=1){var f=a.charCodeAt(e);(f==="\n".charCodeAt(0)||f==="\r".charCodeAt(0))&&(this.charOffset=e+1)}this.offset=d;var g=a.slice(c,this.charOffset);this.onProgressCallback.call(this.thisArg,g)}},c.prototype.onFinish=function(){this.onProgress(),3===this.state&&(this.state=4,0!==this.timeout&&(m(this.timeout),this.timeout=0),this.onFinishCallback.call(this.thisArg))},c.prototype.onReadyStateChange=function(){void 0!=this.xhr&&(4===this.xhr.readyState?0===this.xhr.status?this.onFinish():this.onFinish():3===this.xhr.readyState?this.onProgress():2===this.xhr.readyState)},c.prototype.onTimeout2=function(){this.timeout=0;var b=/^data\:([^,]*?)(base64)?,([\S]*)$/.exec(this.url),c=b[1],d="base64"===b[2]?a.atob(b[3]):decodeURIComponent(b[3]);1===this.state&&(this.state=2,this.onStartCallback.call(this.thisArg,200,"OK",c)),(2===this.state||3===this.state)&&(this.state=3,this.onProgressCallback.call(this.thisArg,d)),3===this.state&&(this.state=4,this.onFinishCallback.call(this.thisArg))},c.prototype.onTimeout1=function(){this.timeout=0,this.open(this.url,this.withCredentials)},c.prototype.onTimeout0=function(){var a=this;this.timeout=l(function(){a.onTimeout0()},500),3===this.xhr.readyState&&this.onProgress()},c.prototype.handleEvent=function(a){"load"===a.type?this.onFinish():"error"===a.type?this.onFinish():"abort"===a.type?this.onFinish():"progress"===a.type?this.onProgress():"readystatechange"===a.type&&this.onReadyStateChange()},c.prototype.open=function(b,c){0!==this.timeout&&(m(this.timeout),this.timeout=0),this.url=b,this.withCredentials=c,this.state=1,this.charOffset=0,this.offset=0;var d=this,e=/^data\:([^,]*?)(?:;base64)?,[\S]*$/.exec(b);if(void 0!=e)return void(this.timeout=l(function(){d.onTimeout2()},0));if((!("ontimeout"in this.xhr)||"sendAsBinary"in this.xhr||"mozAnon"in this.xhr)&&void 0!=a.document&&void 0!=a.document.readyState&&"complete"!==a.document.readyState)return void(this.timeout=l(function(){d.onTimeout1()},4));this.xhr.onload=function(a){d.handleEvent({type:"load"})},this.xhr.onerror=function(){d.handleEvent({type:"error"})},this.xhr.onabort=function(){d.handleEvent({type:"abort"})},this.xhr.onprogress=function(){d.handleEvent({type:"progress"})},this.xhr.onreadystatechange=function(){d.handleEvent({type:"readystatechange"})},this.xhr.open("GET",b,!0),this.xhr.withCredentials=c,this.xhr.responseType="text","setRequestHeader"in this.xhr&&this.xhr.setRequestHeader("Accept","text/event-stream");try{this.xhr.send(void 0)}catch(f){throw f}"readyState"in this.xhr&&void 0!=a.opera&&(this.timeout=l(function(){d.onTimeout0()},0))},c.prototype.cancel=function(){0!==this.state&&4!==this.state&&(this.state=4,this.xhr.onload=n,this.xhr.onerror=n,this.xhr.onabort=n,this.xhr.onprogress=n,this.xhr.onreadystatechange=n,this.xhr.abort(),0!==this.timeout&&(m(this.timeout),this.timeout=0),this.onFinishCallback.call(this.thisArg)),this.state=0},d.prototype.get=function(a){return this._data[a+"~"]},d.prototype.set=function(a,b){this._data[a+"~"]=b},d.prototype["delete"]=function(a){delete this._data[a+"~"]},e.prototype.dispatchEvent=function(a){a.target=this;var b=a.type.toString(),c=this._listeners,d=c.get(b);if(void 0!=d)for(var e=d.length,g=void 0,h=0;e>h;h+=1){g=d[h];try{"function"==typeof g.handleEvent?g.handleEvent(a):g.call(this,a)}catch(i){f(i)}}},e.prototype.addEventListener=function(a,b){a=a.toString();var c=this._listeners,d=c.get(a);void 0==d&&(d=[],c.set(a,d));for(var e=d.length;e>=0;e-=1)if(d[e]===b)return;d.push(b)},e.prototype.removeEventListener=function(a,b){a=a.toString();var c=this._listeners,d=c.get(a);if(void 0!=d){for(var e=d.length,f=[],g=0;e>g;g+=1)d[g]!==b&&f.push(d[g]);0===f.length?c["delete"](a):c.set(a,f)}},h.prototype=g.prototype;var o=a.XMLHttpRequest,p=a.XDomainRequest,q=void 0!=o&&void 0!=(new o).withCredentials,r=q||void 0!=o&&void 0==p?o:p,s=-1,t=0,u=1,v=2,w=3,x=4,y=5,z=6,A=7,B=/^text\/event\-stream;?(\s*charset\=utf\-8)?$/i,C=1e3,D=18e6,E=function(a,b){var c=a;return c!==c&&(c=b),C>c?C:c>D?D:c},F=function(a,b,c){try{"function"==typeof b&&b.call(a,c)}catch(d){f(d)}};j.prototype.onStart=function(a,b,c){if(this.currentState===t)if(void 0==c&&(c=""),200===a&&B.test(c)){this.currentState=u,this.wasActivity=!0,this.retry=this.initialRetry,this.readyState=u,this.es.readyState=u;var d=new g("open");this.es.dispatchEvent(d),F(this.es,this.es.onopen,d)}else if(0!==a){var e="";e=200!==a?"EventSource's response has a status "+a+" "+b.replace(/\s+/g," ")+" that is not 200. Aborting the connection.":"EventSource's response has a Content-Type specifying an unsupported type: "+c.replace(/\s+/g," ")+". Aborting the connection.",f(new Error(e)),this.close();var d=new g("error");this.es.dispatchEvent(d),F(this.es,this.es.onerror,d)}},j.prototype.onProgress=function(a){if(this.currentState===u){var b=a.length;0!==b&&(this.wasActivity=!0);for(var c=0;b>c;c+=1){var d=a.charCodeAt(c);if(this.state===w&&d==="\n".charCodeAt(0))this.state=x;else if(this.state===w&&(this.state=x),d==="\r".charCodeAt(0)||d==="\n".charCodeAt(0)){if(this.state!==x){this.state===y&&(this.valueStart=c+1);var e=a.slice(this.fieldStart,this.valueStart-1),f=a.slice(this.valueStart+(this.valueStart<c&&a.charCodeAt(this.valueStart)===" ".charCodeAt(0)?1:0),c);if("data"===e)this.dataBuffer.push(f);else if("id"===e)this.lastEventIdBuffer=f;else if("event"===e)this.eventTypeBuffer=f;else if("retry"===e)this.initialRetry=E(Number(f),this.initialRetry),this.retry=this.initialRetry;else if("heartbeatTimeout"===e&&(this.heartbeatTimeout=E(Number(f),this.heartbeatTimeout),0!==this.timeout)){m(this.timeout);var g=this;this.timeout=l(function(){g.onTimeout()},this.heartbeatTimeout)}}if(this.state===x){if(0!==this.dataBuffer.length){this.lastEventId=this.lastEventIdBuffer,""===this.eventTypeBuffer&&(this.eventTypeBuffer="message");var i=new h(this.eventTypeBuffer,{data:this.dataBuffer.join("\n"),lastEventId:this.lastEventIdBuffer});if(this.es.dispatchEvent(i),"message"===this.eventTypeBuffer&&F(this.es,this.es.onmessage,i),this.currentState===v)return}this.dataBuffer.length=0,this.eventTypeBuffer=""}this.state=d==="\r".charCodeAt(0)?w:x}else this.state===x&&(this.fieldStart=c,this.state=y),this.state===y?d===":".charCodeAt(0)&&(this.valueStart=c+1,this.state=z):this.state===z&&(this.state=A)}}},j.prototype.onFinish=function(){if(this.currentState===u||this.currentState===t){this.currentState=s,0!==this.timeout&&(m(this.timeout),this.timeout=0),this.retry>16*this.initialRetry&&(this.retry=16*this.initialRetry),this.retry>D&&(this.retry=D);var a=this;this.timeout=l(function(){a.onTimeout()},this.retry),this.retry=2*this.retry+1,this.readyState=t,this.es.readyState=t;var b=new g("error");this.es.dispatchEvent(b),F(this.es,this.es.onerror,b)}},j.prototype.onTimeout=function(){if(this.timeout=0,this.currentState===s){this.wasActivity=!1;var a=this;this.timeout=l(function(){a.onTimeout()},this.heartbeatTimeout),this.currentState=t,this.dataBuffer.length=0,this.eventTypeBuffer="",this.lastEventIdBuffer=this.lastEventId,this.fieldStart=0,this.valueStart=0,this.state=x;var b=this.url.slice(0,5);b="data:"!==b&&"blob:"!==b?this.url+((-1===this.url.indexOf("?",0)?"?":"&")+"lastEventId="+encodeURIComponent(this.lastEventId)+"&r="+(Math.random()+1).toString().slice(2)):this.url;try{this.transport.open(b,this.withCredentials)}catch(c){throw this.close(),c}}else if(this.wasActivity){this.wasActivity=!1;var a=this;this.timeout=l(function(){a.onTimeout()},this.heartbeatTimeout)}else f(new Error("No activity within "+this.heartbeatTimeout+" milliseconds. Reconnecting.")),this.transport.cancel()},j.prototype.close=function(){this.currentState=v,this.transport.cancel(),0!==this.timeout&&(m(this.timeout),this.timeout=0),this.readyState=v,this.es.readyState=v},k.prototype=e.prototype,i.prototype=new k,i.prototype.close=function(){this._internal.close()},k.call(i),q&&(i.prototype.withCredentials=void 0);var G=function(){return void 0!=a.EventSource&&"withCredentials"in a.EventSource.prototype};void 0!=r&&(void 0==a.EventSource||q&&!G())&&(a.NativeEventSource=a.EventSource,a.EventSource=i)}("undefined"!=typeof window?window:this);
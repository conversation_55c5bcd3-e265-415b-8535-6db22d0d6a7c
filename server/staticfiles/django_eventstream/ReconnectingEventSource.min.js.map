{"version": 3, "file": "ReconnectingEventSource.min.js", "mappings": "yJAmCA,MAAaA,UAAqCC,MAC9CC,cACIC,MACE,0MAIN,EAPJ,iCAiBA,MAAqBC,EAyBjBF,YAAYG,EAAmBC,GA+B3B,GArDK,KAAAC,WAAa,EACb,KAAAC,KAAO,EACP,KAAAC,OAAS,EAqBdC,KAAKC,eAAkC,MAAjBL,EAAwBM,OAAOC,OAAO,CAAC,EAAGP,QAAiBQ,EACjFJ,KAAKK,iBAAkB,EAEvBL,KAAKM,aAAe,KACpBN,KAAKO,aAAe,KACpBP,KAAKQ,OAAS,KACdR,KAAKS,WAAa,CAAC,EAEnBT,KAAKL,IAAMA,EAAIe,WACfV,KAAKW,WAAaX,KAAKH,WACvBG,KAAKY,eAAiB,IACtBZ,KAAKa,iBAAmBC,WAAWC,YAER,MAAvBf,KAAKC,iBACDD,KAAKC,eAAee,cACpBhB,KAAKO,aAAeP,KAAKC,eAAee,mBACjChB,KAAKC,eAA4B,aAGxCD,KAAKC,eAAeW,iBACpBZ,KAAKY,eAAiBZ,KAAKC,eAAeW,sBACnCZ,KAAKC,eAA+B,gBAG3CD,KAAKC,eAAeY,mBACpBb,KAAKa,iBAAmBb,KAAKC,eAAeY,wBACrCb,KAAKC,eAAiC,mBAIzB,MAAzBD,KAAKa,kBAA6D,mBAA1Bb,KAAKa,iBAC5C,MAAM,IAAIvB,EAGdU,KAAKiB,iBAAoBC,IAAYlB,KAAKmB,SAASD,EAAM,EAEzDlB,KAAKoB,QACT,CAEAC,cAAcH,GACV,MAAM,IAAI3B,MAAM,0BACpB,CAEA6B,SACI,IAAIzB,EAAMK,KAAKL,IAEXK,KAAKO,gBACqB,IAAtBZ,EAAI2B,QAAQ,KACZ3B,GAAO,IAEPA,GAAO,IAEXA,GAAO,eAAiB4B,mBAAmBvB,KAAKO,eAGpDP,KAAKM,aAAe,IAAIN,KAAKa,iBAAiBlB,EAAKK,KAAKC,gBAExDD,KAAKM,aAAakB,OAAUN,IAAYlB,KAAKyB,QAAQP,EAAM,EAC3DlB,KAAKM,aAAaoB,QAAWR,IAAYlB,KAAK2B,SAAST,EAAM,EAC7DlB,KAAKM,aAAasB,UAAaV,IAAYlB,KAAK4B,UAAUV,EAAM,EAGhE,IAAK,MAAMW,KAAQ3B,OAAO4B,KAAK9B,KAAKS,YAChCT,KAAKM,aAAayB,iBAAiBF,EAAM7B,KAAKiB,iBAEtD,CAEAQ,QAAQP,GACoB,IAApBlB,KAAKW,aACLX,KAAKW,WAAa,EAClBX,KAAKwB,OAAON,GAEpB,CAEAS,SAAST,GAML,GALwB,IAApBlB,KAAKW,aACLX,KAAKW,WAAa,EAClBX,KAAK0B,QAAQR,IAGblB,KAAKM,cAC+B,IAAjCN,KAAKM,aAAaK,WAAkB,CAEnCX,KAAKM,aAAa0B,QAClBhC,KAAKM,aAAe,KAGpB,MAAM2B,EAAUC,KAAKC,MAAMnC,KAAKY,eAAiBsB,KAAKE,UACtDpC,KAAKQ,OAAS6B,YAAW,IAAMrC,KAAKoB,UAAUa,E,CAG1D,CAEAd,SAASD,GACDA,aAAiBoB,eACjBtC,KAAKO,aAAeW,EAAMF,aAG9B,MAAMuB,EAAmBvC,KAAKS,WAAWS,EAAMW,MAC/C,GAAwB,MAApBU,EAEA,IAAK,MAAMC,IAAY,IAAID,GACvBC,EAASC,KAAKzC,KAAMkB,GAIT,YAAfA,EAAMW,MACN7B,KAAK4B,UAAUV,EAEvB,CAEAM,OAAON,GAEP,CAEAQ,QAAQR,GAER,CAEAU,UAAUV,GAEV,CAEAc,QACQhC,KAAKQ,SACLkC,aAAa1C,KAAKQ,QAClBR,KAAKQ,OAAS,MAGdR,KAAKM,eACLN,KAAKM,aAAa0B,QAClBhC,KAAKM,aAAe,MAGxBN,KAAKW,WAAa,CACtB,CAIAoB,iBAAmCF,EAASW,EAAgCG,GAGlEd,KAAQ7B,KAAKS,aACfT,KAAKS,WAAWoB,GAAQ,GACC,MAArB7B,KAAKM,cACLN,KAAKM,aAAayB,iBAAiBF,EAAM7B,KAAKiB,mBAItD,MAAMsB,EAAmBvC,KAAKS,WAAWoB,GACrCe,MAAMC,QAAQN,KAAsBA,EAAiBO,SAASN,IAC9DD,EAAiBQ,KAAKP,EAE9B,CAIAQ,oBAAsCnB,EAASW,EAAgCG,GAG3E,MAAMJ,EAAmBvC,KAAKS,WAAWoB,GACzC,GAAwB,MAApBU,EAA0B,CAE1B,OAAY,CACR,MAAMU,EAAQV,EAAiBjB,QAAQkB,GACvC,IAAe,IAAXS,EACA,MAEJV,EAAiBW,OAAOD,EAAO,E,CAG/BV,EAAiBY,QAAU,WACpBnD,KAAKS,WAAWoB,GACE,MAArB7B,KAAKM,cACLN,KAAKM,aAAa0C,oBAAoBnB,EAAM7B,KAAKiB,kB,CAIjE,EA5MJ,YAQoB,EAAApB,WAAa,EACb,EAAAC,KAAO,EACP,EAAAC,OAAS,C,GC7DzBqD,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBlD,IAAjBmD,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CAGjDE,QAAS,CAAC,GAOX,OAHAE,EAAoBJ,GAAUG,EAAQA,EAAOD,QAASH,GAG/CI,EAAOD,OACf,C,wECtBA,cACAtD,OAAOC,OAAOwD,OAAQ,CACpBjE,wBAAA,UACAJ,6BAAA,EAAAA,8B", "sources": ["webpack://_ReconnectingEventSource/./src/reconnecting-eventsource.ts", "webpack://_ReconnectingEventSource/webpack/bootstrap", "webpack://_ReconnectingEventSource/./src/main.browser.ts"], "sourcesContent": ["// MIT License:\n//\n// Copyright (C) 2022 Fanout, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to\n// deal in the Software without restriction, including without limitation the\n// rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n// sell copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n// IN THE SOFTWARE.\n\nexport interface ReconnectingEventSourceInit extends EventSourceInit {\n    // the maximum time to wait before attempting to reconnect in ms, default `3000`\n    // note: wait time is randomised to prevent all clients from attempting to reconnect simultaneously\n    max_retry_time?: number;\n\n    // the EventSource class to wrap. This allows the use of a polyfill or alternate\n    // implementation instead of the platform-provided EventSource class.\n    eventSourceClass?: typeof EventSource;\n\n    // the last event\n    lastEventId?: string;\n}\n\nexport class EventSourceNotAvailableError extends Error {\n    constructor() {\n        super(\n          'EventSource not available.\\n' +\n          'Consider loading an EventSource polyfill and making it available globally as EventSource, ' +\n          'or passing one in as eventSourceClass to the ReconnectingEventSource constructor.'\n        );\n    }\n}\n\ntype EventType<T extends string> = T extends keyof EventSourceEventMap ? EventSourceEventMap[T] : MessageEvent<any>;\ntype EventListenerType<T extends string> = (this: EventSource, event: EventType<T>) => any;\n\ntype Listeners = {\n  [name: string]: ((this: EventSource, event: Event) => any)[];\n};\n\nexport default class ReconnectingEventSource implements EventSource {\n\n    readonly _configuration: ReconnectingEventSourceInit | undefined;\n    readonly CONNECTING = 0;\n    readonly OPEN = 1;\n    readonly CLOSED = 2;\n\n    // Evidently these are to exist both on the class and the instance\n    static readonly CONNECTING = 0;\n    static readonly OPEN = 1;\n    static readonly CLOSED = 2;\n\n    _eventSource: EventSource | null;\n    _lastEventId: string | null;\n    _timer: NodeJS.Timer | null;\n    _listeners: Listeners;\n    _onevent_wrapped: (this: EventSource, ev: Event) => any;\n\n    readyState: 0 | 1 | 2;\n    url: string;\n    withCredentials: boolean;\n\n    readonly max_retry_time: number;\n    eventSourceClass: typeof EventSource;\n\n    constructor(url: string | URL, configuration?: ReconnectingEventSourceInit) {\n        this._configuration = configuration != null ? Object.assign({}, configuration) : undefined;\n        this.withCredentials = false;\n\n        this._eventSource = null;\n        this._lastEventId = null;\n        this._timer = null;\n        this._listeners = {};\n\n        this.url = url.toString();\n        this.readyState = this.CONNECTING;\n        this.max_retry_time = 3000;\n        this.eventSourceClass = globalThis.EventSource;\n\n        if (this._configuration != null) {\n            if (this._configuration.lastEventId) {\n                this._lastEventId = this._configuration.lastEventId;\n                delete this._configuration['lastEventId'];\n            }\n\n            if (this._configuration.max_retry_time) {\n                this.max_retry_time = this._configuration.max_retry_time;\n                delete this._configuration['max_retry_time'];\n            }\n\n            if (this._configuration.eventSourceClass) {\n                this.eventSourceClass = this._configuration.eventSourceClass;\n                delete this._configuration['eventSourceClass'];\n            }\n        }\n\n        if(this.eventSourceClass == null || typeof this.eventSourceClass !== 'function') {\n            throw new EventSourceNotAvailableError();\n        }\n\n        this._onevent_wrapped = (event) => { this._onevent(event); };\n\n        this._start();\n    }\n\n    dispatchEvent(event: Event): boolean {\n        throw new Error(\"Method not implemented.\");\n    }\n\n    _start() {\n        let url = this.url;\n\n        if (this._lastEventId) {\n            if (url.indexOf('?') === -1) {\n                url += '?';\n            } else {\n                url += '&';\n            }\n            url += 'lastEventId=' + encodeURIComponent(this._lastEventId);\n        }\n\n        this._eventSource = new this.eventSourceClass(url, this._configuration);\n\n        this._eventSource.onopen = (event) => { this._onopen(event); };\n        this._eventSource.onerror = (event) => { this._onerror(event); };\n        this._eventSource.onmessage = (event) => { this.onmessage(event); };\n\n        // apply listen types\n        for (const type of Object.keys(this._listeners)) {\n            this._eventSource.addEventListener(type, this._onevent_wrapped);\n        }\n    }\n\n    _onopen(event: Event) {\n        if (this.readyState === 0) {\n            this.readyState = 1;\n            this.onopen(event);\n        }\n    }\n\n    _onerror(event: Event) {\n        if (this.readyState === 1) {\n            this.readyState = 0;\n            this.onerror(event);\n        }\n\n        if (this._eventSource) {\n            if(this._eventSource.readyState === 2) {\n                // reconnect with new object\n                this._eventSource.close();\n                this._eventSource = null;\n\n                // reconnect after random timeout < max_retry_time\n                const timeout = Math.round(this.max_retry_time * Math.random());\n                this._timer = setTimeout(() => this._start(), timeout);\n            }\n        }\n    }\n\n    _onevent(event: Event) {\n        if (event instanceof MessageEvent) {\n            this._lastEventId = event.lastEventId;\n        }\n\n        const listenersForType = this._listeners[event.type];\n        if (listenersForType != null) {\n            // operate on a copy\n            for (const listener of [...listenersForType]) {\n                listener.call(this, event);\n            }\n        }\n\n        if (event.type === 'message') {\n            this.onmessage(event as MessageEvent);\n        }\n    }\n\n    onopen(event: Event) {\n        // may be overridden\n    }\n\n    onerror(event: Event) {\n        // may be overridden\n    }\n\n    onmessage(event: MessageEvent) {\n        // may be overridden\n    }\n\n    close() {\n        if (this._timer) {\n            clearTimeout(this._timer);\n            this._timer = null;\n        }\n\n        if (this._eventSource) {\n            this._eventSource.close();\n            this._eventSource = null;\n        }\n\n        this.readyState = 2;\n    }\n\n    addEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;\n    addEventListener(type: string, listener: (this: EventSource, event: MessageEvent<any>) => any, options?: boolean | AddEventListenerOptions): void;\n    addEventListener<K extends string>(type: K, listener: EventListenerType<K>, options?: boolean | AddEventListenerOptions): void {\n        // We don't support the options arg at the moment\n\n        if (!(type in this._listeners)) {\n            this._listeners[type] = [];\n            if (this._eventSource != null) {\n                this._eventSource.addEventListener(type, this._onevent_wrapped);\n            }\n        }\n\n        const listenersForType = this._listeners[type] as EventListenerType<K>[];\n        if (Array.isArray(listenersForType) && !listenersForType.includes(listener)) {\n            listenersForType.push(listener);\n        }\n    }\n\n    removeEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | EventListenerOptions): void;\n    removeEventListener(type: string, listener: (this: EventSource, event: MessageEvent<any>) => any, options?: boolean | EventListenerOptions): void;\n    removeEventListener<K extends string>(type: K, listener: EventListenerType<K>, options?: boolean | EventListenerOptions): void {\n        // We don't support the options arg at the moment\n\n        const listenersForType = this._listeners[type] as EventListenerType<K>[];\n        if (listenersForType != null) {\n            // eslint-disable-next-line no-constant-condition\n            while(true) {\n                const index = listenersForType.indexOf(listener);\n                if (index === -1) {\n                    break;\n                }\n                listenersForType.splice(index, 1);\n            }\n\n            if (listenersForType.length <= 0) {\n                delete this._listeners[type];\n                if (this._eventSource != null) {\n                    this._eventSource.removeEventListener(type, this._onevent_wrapped);\n                }\n            }\n        }\n    }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "import ReconnectingEventSource, { EventSourceNotAvailableError } from './reconnecting-eventsource';\nObject.assign(window, {\n  ReconnectingEventSource,\n  EventSourceNotAvailableError,\n});\n"], "names": ["EventSourceNotAvailableError", "Error", "constructor", "super", "ReconnectingEventSource", "url", "configuration", "CONNECTING", "OPEN", "CLOSED", "this", "_configuration", "Object", "assign", "undefined", "withCredentials", "_eventSource", "_lastEventId", "_timer", "_listeners", "toString", "readyState", "max_retry_time", "eventSourceClass", "globalThis", "EventSource", "lastEventId", "_onevent_wrapped", "event", "_onevent", "_start", "dispatchEvent", "indexOf", "encodeURIComponent", "onopen", "_onopen", "onerror", "_onerror", "onmessage", "type", "keys", "addEventListener", "close", "timeout", "Math", "round", "random", "setTimeout", "MessageEvent", "listenersForType", "listener", "call", "clearTimeout", "options", "Array", "isArray", "includes", "push", "removeEventListener", "index", "splice", "length", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "__webpack_modules__", "window"], "sourceRoot": ""}
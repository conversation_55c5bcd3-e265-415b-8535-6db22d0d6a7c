"""
URL configuration per il server MCP Django.

Questo file definisce il routing principale per il server MCP.
Include gli endpoint per:
- API MCP (inizializzazione, tools, eventi)
- Admin Django
- Server-Sent Events per comunicazione real-time

Documentazione MCP: https://modelcontextprotocol.io/
"""

from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    # Admin Django per gestione dati
    path('admin/', admin.site.urls),
    
    # API MCP principale - implementa il protocollo MCP
    # Tutti gli endpoint MCP sono sotto /api/mcp/
    path('api/mcp/', include('mcp_api.urls')),
    
    # Server-Sent Events per comunicazione real-time
    # Necessario per eventi asincroni nel protocollo MCP
    path('events/', include('django_eventstream.urls')),
]

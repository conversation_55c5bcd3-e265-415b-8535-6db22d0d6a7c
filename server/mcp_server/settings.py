"""
Configurazione Django per il server MCP.

Questo file contiene tutte le impostazioni necessarie per implementare
un server Model Context Protocol usando Django e Django REST Framework.

Documentazione MCP: https://modelcontextprotocol.io/
Specifiche protocollo: https://spec.modelcontextprotocol.io/
"""

import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

BASE_DIR = Path(__file__).resolve().parent.parent

# Configurazione di sicurezza Django
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-mcp-learning-key-change-in-production')
DEBUG = os.getenv('DEBUG', '1') == '1'
ALLOWED_HOSTS = ['*']  # Per sviluppo - restringere in produzione

# Applicazioni Django installate
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # Django REST Framework per API RESTful
    'rest_framework',
    
    # CORS per permettere richieste cross-origin dal client
    'corsheaders',
    
    # Django EventStream per Server-Sent Events (SSE)
    # Necessario per eventi real-time nel protocollo MCP
    'django_eventstream',
    
    # Django Extensions per shell_plus e comandi utili (temporaneamente disabilitato)
    # 'django_extensions',

    # App principale MCP - implementa il protocollo
    'mcp_api',
]

# Middleware Django
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # CORS deve essere primo
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'mcp_server.urls'

# Configurazione template Django
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'mcp_server.wsgi.application'

# Database PostgreSQL - necessario per persistenza dati MCP
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'mcp_db'),
        'USER': os.getenv('DB_USER', 'mcp_user'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'mcp_password'),
        'HOST': os.getenv('DB_HOST', 'db'),
        'PORT': os.getenv('DB_PORT', '5432'),
    }
}

# Configurazione Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
    ],
    # Per sviluppo - aggiungere autenticazione in produzione
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
}

# Configurazione CORS - permette richieste da qualsiasi origine per sviluppo
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Configurazione internazionalizzazione
LANGUAGE_CODE = 'it-it'
TIME_ZONE = 'Europe/Rome'
USE_I18N = True
USE_TZ = True

# File statici
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Configurazione logging per debugging MCP
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'mcp_api': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

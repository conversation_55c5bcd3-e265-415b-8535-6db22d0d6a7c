FROM python:3.11-slim

WORKDIR /app

# Installa dipendenze sistema per PostgreSQL
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copia e installa dipendenze Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copia codice applicazione
COPY . .

# Espone porta Django
EXPOSE 8000

# Comando di default (sovrascritto in docker-compose)
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]

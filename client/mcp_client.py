#!/usr/bin/env python3
"""
Client Python per il protocollo MCP (Model Context Protocol).

Questo client implementa le funzionalità principali del protocollo MCP:
- Inizializzazione e handshake con il server
- Discovery e chiamata di tools
- Ascolto di eventi via Server-Sent Events (SSE)

Documentazione MCP: https://modelcontextprotocol.io/
Specifiche: https://spec.modelcontextprotocol.io/
"""

import os
import json
import time
import threading
import requests
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.json import JSON
import sseclient

# Configurazione console per output colorato
console = Console()


@dataclass
class MCPClientConfig:
    """
    Configurazione per il client MCP.
    """
    server_url: str = "http://localhost:8000"
    client_name: str = "MCP Learning Client"
    client_version: str = "1.0.0"
    timeout: int = 30


class MCPClient:
    """
    Client per il protocollo Model Context Protocol (MCP).
    
    Implementa le funzionalità principali per comunicare con un server MCP:
    - Handshake e negoziazione capacità
    - Discovery e esecuzione tools
    - Gestione eventi real-time
    
    Riferimento: https://spec.modelcontextprotocol.io/
    """
    
    def __init__(self, config: MCPClientConfig):
        """
        Inizializza il client MCP.
        
        Args:
            config: Configurazione del client
        """
        self.config = config
        self.session_id: Optional[str] = None
        self.server_info: Optional[Dict] = None
        self.server_capabilities: Optional[Dict] = None
        self.available_tools: List[Dict] = []
        self.is_connected = False
        self.events_thread: Optional[threading.Thread] = None
        self.stop_events = False
        
        # Configurazione HTTP session
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': f'{config.client_name}/{config.client_version}'
        })
    
    def initialize(self) -> bool:
        """
        Inizializza la connessione MCP con il server.
        
        Implementa l'handshake del protocollo MCP per negoziare
        le capacità e stabilire una sessione.
        
        Returns:
            bool: True se inizializzazione riuscita
        """
        console.print("[bold blue]🔄 Inizializzazione connessione MCP...[/bold blue]")
        
        # Prepara richiesta di inizializzazione secondo specifiche MCP
        init_request = {
            "clientInfo": {
                "name": self.config.client_name,
                "version": self.config.client_version
            },
            "capabilities": {
                "tools": {
                    "supportsProgress": False  # Non supportiamo progress per ora
                },
                "resources": {
                    "subscribe": False  # Non supportiamo subscription
                },
                "prompts": {
                    "listChanged": False  # Non supportiamo prompts
                }
            }
        }
        
        try:
            # Invia richiesta di inizializzazione
            response = self.session.post(
                f"{self.config.server_url}/api/mcp/initialize/",
                json=init_request,
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Salva informazioni della sessione
                self.session_id = data.get('sessionId')
                self.server_info = data.get('serverInfo')
                self.server_capabilities = data.get('capabilities')
                self.is_connected = True
                
                # Aggiorna header con session ID
                self.session.headers['X-MCP-Session-ID'] = self.session_id
                
                console.print("[bold green]✅ Connessione MCP stabilita![/bold green]")
                self._display_server_info()
                
                return True
            else:
                console.print(f"[bold red]❌ Errore inizializzazione: {response.status_code}[/bold red]")
                console.print(response.text)
                return False
                
        except requests.exceptions.RequestException as e:
            console.print(f"[bold red]❌ Errore di connessione: {e}[/bold red]")
            return False
    
    def _display_server_info(self):
        """
        Mostra informazioni sul server connesso.
        """
        if not self.server_info:
            return
        
        info_table = Table(title="Informazioni Server MCP")
        info_table.add_column("Proprietà", style="cyan")
        info_table.add_column("Valore", style="green")
        
        info_table.add_row("Nome", self.server_info.get('name', 'N/A'))
        info_table.add_row("Versione", self.server_info.get('version', 'N/A'))
        info_table.add_row("Descrizione", self.server_info.get('description', 'N/A'))
        info_table.add_row("Session ID", self.session_id[:8] + "..." if self.session_id else 'N/A')
        
        console.print(info_table)
    
    def discover_tools(self) -> bool:
        """
        Scopre i tools disponibili sul server MCP.
        
        Implementa il tool discovery del protocollo MCP.
        
        Returns:
            bool: True se discovery riuscito
        """
        if not self.is_connected:
            console.print("[bold red]❌ Non connesso al server MCP[/bold red]")
            return False
        
        console.print("[bold blue]🔍 Scoperta tools disponibili...[/bold blue]")
        
        try:
            response = self.session.get(
                f"{self.config.server_url}/api/mcp/tools/",
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                self.available_tools = data.get('tools', [])
                
                console.print(f"[bold green]✅ Trovati {len(self.available_tools)} tools[/bold green]")
                self._display_tools()
                
                return True
            else:
                console.print(f"[bold red]❌ Errore discovery tools: {response.status_code}[/bold red]")
                return False
                
        except requests.exceptions.RequestException as e:
            console.print(f"[bold red]❌ Errore di connessione: {e}[/bold red]")
            return False
    
    def _display_tools(self):
        """
        Mostra i tools disponibili in formato tabella.
        """
        if not self.available_tools:
            console.print("[yellow]⚠️ Nessun tool disponibile[/yellow]")
            return
        
        tools_table = Table(title="Tools MCP Disponibili")
        tools_table.add_column("Nome", style="cyan")
        tools_table.add_column("Descrizione", style="green")
        
        for tool in self.available_tools:
            tools_table.add_row(
                tool.get('name', 'N/A'),
                tool.get('description', 'N/A')[:60] + "..." if len(tool.get('description', '')) > 60 else tool.get('description', 'N/A')
            )
        
        console.print(tools_table)

    def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Optional[Dict]:
        """
        Chiama un tool sul server MCP.

        Args:
            tool_name: Nome del tool da chiamare
            arguments: Parametri per il tool

        Returns:
            dict: Risultato dell'esecuzione o None se errore
        """
        if not self.is_connected:
            console.print("[bold red]❌ Non connesso al server MCP[/bold red]")
            return None

        if arguments is None:
            arguments = {}

        console.print(f"[bold blue]🔧 Chiamata tool '{tool_name}'...[/bold blue]")

        # Prepara richiesta secondo specifiche MCP
        call_request = {
            "name": tool_name,
            "arguments": arguments
        }

        try:
            response = self.session.post(
                f"{self.config.server_url}/api/mcp/tools/call/",
                json=call_request,
                timeout=self.config.timeout
            )

            if response.status_code == 200:
                data = response.json()

                if data.get('isError', False):
                    console.print(f"[bold red]❌ Errore tool: {data.get('error', 'Errore sconosciuto')}[/bold red]")
                    return None
                else:
                    console.print(f"[bold green]✅ Tool '{tool_name}' eseguito con successo[/bold green]")
                    self._display_tool_result(data)
                    return data
            else:
                console.print(f"[bold red]❌ Errore chiamata tool: {response.status_code}[/bold red]")
                console.print(response.text)
                return None

        except requests.exceptions.RequestException as e:
            console.print(f"[bold red]❌ Errore di connessione: {e}[/bold red]")
            return None

    def _display_tool_result(self, result_data: Dict):
        """
        Mostra il risultato di una chiamata tool.
        """
        call_id = result_data.get('callId', 'N/A')
        content = result_data.get('content', {})

        # Crea pannello con risultato
        result_panel = Panel(
            JSON.from_data(content),
            title=f"Risultato Tool (Call ID: {call_id[:8]}...)",
            border_style="green"
        )
        console.print(result_panel)

    def start_event_listener(self):
        """
        Avvia l'ascolto degli eventi SSE dal server.

        Implementa la ricezione di eventi real-time secondo
        le specifiche MCP per comunicazione asincrona.
        """
        if not self.is_connected:
            console.print("[bold red]❌ Non connesso al server MCP[/bold red]")
            return

        console.print("[bold blue]🎧 Avvio ascolto eventi SSE...[/bold blue]")

        def event_listener():
            """
            Thread per ascolto eventi SSE.
            """
            try:
                # Connessione SSE al server
                response = requests.get(
                    f"{self.config.server_url}/api/mcp/events/",
                    headers={'Accept': 'text/event-stream'},
                    stream=True,
                    timeout=None
                )

                if response.status_code == 200:
                    client = sseclient.SSEClient(response)

                    for event in client.events():
                        if self.stop_events:
                            break

                        try:
                            event_data = json.loads(event.data)
                            self._handle_event(event_data)
                        except json.JSONDecodeError:
                            console.print(f"[yellow]⚠️ Evento SSE non valido: {event.data}[/yellow]")
                        except Exception as e:
                            console.print(f"[red]❌ Errore gestione evento: {e}[/red]")
                else:
                    console.print(f"[bold red]❌ Errore connessione SSE: {response.status_code}[/bold red]")

            except requests.exceptions.RequestException as e:
                console.print(f"[bold red]❌ Errore connessione eventi: {e}[/bold red]")

        # Avvia thread per eventi
        self.events_thread = threading.Thread(target=event_listener, daemon=True)
        self.events_thread.start()

    def _handle_event(self, event_data: Dict):
        """
        Gestisce un evento ricevuto dal server.

        Args:
            event_data: Dati dell'evento
        """
        event_type = event_data.get('type', 'unknown')

        if event_type == 'connected':
            console.print("[bold green]🔗 Connesso al stream eventi[/bold green]")
        elif event_type == 'heartbeat':
            # Heartbeat silenzioso (non mostrare)
            pass
        else:
            # Altri eventi
            event_panel = Panel(
                JSON.from_data(event_data),
                title=f"Evento MCP: {event_type}",
                border_style="blue"
            )
            console.print(event_panel)

    def get_server_info(self) -> Optional[Dict]:
        """
        Ottiene informazioni dettagliate sul server.

        Returns:
            dict: Informazioni server o None se errore
        """
        if not self.is_connected:
            console.print("[bold red]❌ Non connesso al server MCP[/bold red]")
            return None

        try:
            response = self.session.get(
                f"{self.config.server_url}/api/mcp/info/",
                timeout=self.config.timeout
            )

            if response.status_code == 200:
                return response.json()
            else:
                console.print(f"[bold red]❌ Errore info server: {response.status_code}[/bold red]")
                return None

        except requests.exceptions.RequestException as e:
            console.print(f"[bold red]❌ Errore di connessione: {e}[/bold red]")
            return None

    def disconnect(self):
        """
        Disconnette dal server MCP.
        """
        console.print("[bold yellow]🔌 Disconnessione dal server MCP...[/bold yellow]")

        # Ferma thread eventi
        self.stop_events = True
        if self.events_thread and self.events_thread.is_alive():
            self.events_thread.join(timeout=2)

        # Reset stato
        self.is_connected = False
        self.session_id = None
        self.server_info = None
        self.server_capabilities = None
        self.available_tools = []

        console.print("[bold green]✅ Disconnesso[/bold green]")


def demo_calculator(client: MCPClient):
    """
    Demo interattivo del tool calcolatrice.

    Args:
        client: Client MCP connesso
    """
    console.print("\n[bold cyan]🧮 Demo Calcolatrice MCP[/bold cyan]")

    # Esempi di operazioni
    operations = [
        {"operation": "add", "a": 10, "b": 5},
        {"operation": "subtract", "a": 20, "b": 8},
        {"operation": "multiply", "a": 7, "b": 6},
        {"operation": "divide", "a": 15, "b": 3},
        {"operation": "divide", "a": 10, "b": 0},  # Test divisione per zero
    ]

    for op in operations:
        console.print(f"\n[blue]Operazione: {op['a']} {op['operation']} {op['b']}[/blue]")
        result = client.call_tool("calculator", op)
        time.sleep(1)  # Pausa per leggibilità


def interactive_demo(client: MCPClient):
    """
    Demo interattivo per testare il client MCP.

    Args:
        client: Client MCP connesso
    """
    console.print("\n[bold green]🎮 Demo Interattivo MCP[/bold green]")
    console.print("Comandi disponibili:")
    console.print("1. [cyan]tools[/cyan] - Mostra tools disponibili")
    console.print("2. [cyan]calc[/cyan] - Demo calcolatrice")
    console.print("3. [cyan]info[/cyan] - Informazioni server")
    console.print("4. [cyan]events[/cyan] - Avvia ascolto eventi")
    console.print("5. [cyan]quit[/cyan] - Esci")

    while True:
        try:
            command = input("\n> ").strip().lower()

            if command == "quit" or command == "q":
                break
            elif command == "tools":
                client.discover_tools()
            elif command == "calc":
                demo_calculator(client)
            elif command == "info":
                info = client.get_server_info()
                if info:
                    info_panel = Panel(
                        JSON.from_data(info),
                        title="Informazioni Server",
                        border_style="cyan"
                    )
                    console.print(info_panel)
            elif command == "events":
                client.start_event_listener()
                console.print("[green]Eventi SSE avviati in background[/green]")
            else:
                console.print("[yellow]Comando non riconosciuto[/yellow]")

        except KeyboardInterrupt:
            break
        except EOFError:
            break


def main():
    """
    Funzione principale del client MCP.

    Dimostra l'uso completo del protocollo MCP:
    1. Inizializzazione e handshake
    2. Discovery tools
    3. Esecuzione tools
    4. Ascolto eventi SSE
    """
    # Banner di benvenuto
    console.print(Panel.fit(
        "[bold blue]Client MCP Learning[/bold blue]\n"
        "Implementazione educativa del Model Context Protocol\n"
        "https://modelcontextprotocol.io/",
        border_style="blue"
    ))

    # Configurazione da variabili ambiente
    server_url = os.getenv('MCP_SERVER_URL', 'http://localhost:8000')
    config = MCPClientConfig(server_url=server_url)

    console.print(f"[blue]Server MCP: {config.server_url}[/blue]")

    # Crea client
    client = MCPClient(config)

    try:
        # 1. Inizializzazione MCP
        if not client.initialize():
            console.print("[bold red]❌ Impossibile connettersi al server MCP[/bold red]")
            return

        # 2. Discovery tools
        if not client.discover_tools():
            console.print("[bold yellow]⚠️ Nessun tool disponibile[/bold yellow]")

        # 3. Avvia ascolto eventi in background
        client.start_event_listener()

        # 4. Demo automatico della calcolatrice
        if any(tool['name'] == 'calculator' for tool in client.available_tools):
            demo_calculator(client)

        # 5. Demo interattivo
        interactive_demo(client)

    except KeyboardInterrupt:
        console.print("\n[yellow]Interruzione da utente[/yellow]")
    except Exception as e:
        console.print(f"\n[bold red]❌ Errore inaspettato: {e}[/bold red]")
    finally:
        # Cleanup
        client.disconnect()
        console.print("\n[bold blue]👋 Arrivederci![/bold blue]")


if __name__ == "__main__":
    main()

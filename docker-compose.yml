version: '3.8'

services:
  # Database PostgreSQL per persistenza dati MCP
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: mcp_db
      POSTGRES_USER: mcp_user
      POSTGRES_PASSWORD: mcp_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mcp_user -d mcp_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Server Django MCP - Implementa il protocollo MCP lato server
  # Espone API RESTful per inizializzazione, tools e eventi SSE
  server:
    build: ./server
    ports:
      - "8000:8000"
    environment:
      - DEBUG=1
      - DATABASE_URL=******************************************/mcp_db
    volumes:
      - ./server:/app
    depends_on:
      db:
        condition: service_healthy
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"

  # Client Python MCP - Implementa il protocollo MCP lato client
  # Si connette al server per inizializzazione, chiamate tools e eventi
  client:
    build: ./client
    environment:
      - MCP_SERVER_URL=http://server:8000
    volumes:
      - ./client:/app
    depends_on:
      - server
    # Il client rimane in attesa per esecuzione manuale
    command: tail -f /dev/null

volumes:
  postgres_data:

#!/bin/bash

# Script per testare il progetto MCP Learning
# Esegue test di base per verificare che tutto funzioni

echo "🧪 Test del progetto MCP Learning"
echo "================================="

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per stampare messaggi colorati
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test 1: Verifica che i container siano in esecuzione
print_status "Verifico stato dei container..."
if docker compose ps | grep -q "Up"; then
    print_success "Container in esecuzione"
else
    print_error "Container non in esecuzione. Esegui: docker compose up -d"
    exit 1
fi

# Test 2: Test endpoint di inizializzazione MCP
print_status "Test endpoint inizializzazione MCP..."
response=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "clientInfo": {
            "name": "Test Client",
            "version": "1.0.0"
        },
        "capabilities": {}
    }' \
    http://localhost:8000/api/mcp/initialize/ \
    -o /tmp/mcp_init_response.json)

if [ "$response" = "200" ]; then
    print_success "Inizializzazione MCP funzionante"
    session_id=$(cat /tmp/mcp_init_response.json | python3 -c "import sys, json; print(json.load(sys.stdin)['sessionId'])")
    print_status "Session ID: ${session_id:0:8}..."
else
    print_error "Inizializzazione MCP fallita (HTTP $response)"
    cat /tmp/mcp_init_response.json
    exit 1
fi

# Test 3: Test listing tools
print_status "Test listing tools..."
response=$(curl -s -w "%{http_code}" \
    http://localhost:8000/api/mcp/tools/ \
    -o /tmp/mcp_tools_response.json)

if [ "$response" = "200" ]; then
    print_success "Listing tools funzionante"
    tools_count=$(cat /tmp/mcp_tools_response.json | python3 -c "import sys, json; print(len(json.load(sys.stdin)['tools']))")
    print_status "Tools disponibili: $tools_count"
else
    print_error "Listing tools fallito (HTTP $response)"
    exit 1
fi

# Test 4: Test chiamata tool calcolatrice
print_status "Test tool calcolatrice..."
response=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -H "X-MCP-Session-ID: $session_id" \
    -d '{
        "name": "calculator",
        "arguments": {
            "operation": "add",
            "a": 10,
            "b": 5
        }
    }' \
    http://localhost:8000/api/mcp/tools/call/ \
    -o /tmp/mcp_calc_response.json)

if [ "$response" = "200" ]; then
    print_success "Tool calcolatrice funzionante"
    result=$(cat /tmp/mcp_calc_response.json | python3 -c "import sys, json; data=json.load(sys.stdin); print(data['content']['result'] if not data.get('isError') else 'ERRORE')")
    print_status "Risultato 10 + 5 = $result"
else
    print_error "Tool calcolatrice fallito (HTTP $response)"
    cat /tmp/mcp_calc_response.json
    exit 1
fi

# Test 5: Test info server
print_status "Test info server..."
response=$(curl -s -w "%{http_code}" \
    http://localhost:8000/api/mcp/info/ \
    -o /tmp/mcp_info_response.json)

if [ "$response" = "200" ]; then
    print_success "Info server funzionante"
    server_name=$(cat /tmp/mcp_info_response.json | python3 -c "import sys, json; print(json.load(sys.stdin)['name'])")
    print_status "Server: $server_name"
else
    print_error "Info server fallito (HTTP $response)"
    exit 1
fi

# Test 6: Test client Python (esecuzione rapida)
print_status "Test client Python..."
if docker compose exec -T client python -c "
import sys
sys.path.append('/app')
from mcp_client import MCPClient, MCPClientConfig
import os

config = MCPClientConfig(server_url='http://server:8000')
client = MCPClient(config)

if client.initialize():
    print('✅ Client Python funzionante')
    client.disconnect()
    exit(0)
else:
    print('❌ Client Python fallito')
    exit(1)
" 2>/dev/null; then
    print_success "Client Python funzionante"
else
    print_warning "Client Python potrebbe avere problemi"
fi

# Cleanup file temporanei
rm -f /tmp/mcp_*.json

echo ""
print_success "🎉 Tutti i test completati con successo!"
echo ""
echo "Per testare manualmente:"
echo "  • Server: http://localhost:8000/api/mcp/"
echo "  • Client: docker compose exec client python mcp_client.py"
echo "  • Admin: http://localhost:8000/admin/ (crea superuser prima)"
echo ""
echo "Per vedere i logs:"
echo "  • Server: docker compose logs server"
echo "  • Client: docker compose logs client"
echo "  • Database: docker compose logs db"

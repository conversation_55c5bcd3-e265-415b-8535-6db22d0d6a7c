# Progetto MCP Learning - Django + Docker

Questo progetto implementa un server e client MCP (Model Context Protocol) usando Django, Django REST Framework e Docker per scopi educativi.

## Cos'è MCP?

Il Model Context Protocol (MCP) è un protocollo open-source sviluppato da Anthropic che permette agli assistenti AI di connettersi in modo sicuro a diverse fonti di dati e strumenti esterni.

**Risorse ufficiali:**
- [Documentazione MCP](https://modelcontextprotocol.io/)
- [Specifiche del protocollo](https://spec.modelcontextprotocol.io/)
- [Repository GitHub](https://github.com/modelcontextprotocol)

## Architettura del Progetto

```
mcp-learning/
├── server/                 # Server Django MCP
│   ├── mcp_server/        # Progetto Django principale
│   ├── mcp_api/           # App Django per API MCP
│   ├── requirements.txt
│   └── Dockerfile
├── client/                # Client Python MCP
│   ├── mcp_client.py     # Client principale
│   ├── requirements.txt
│   └── Dockerfile
├── docker-compose.yml     # Orchestrazione container
└── README.md
```

## Componenti Principali

### Server MCP (Django)
- **Inizializzazione**: Endpoint per stabilire connessione MCP
- **Tools**: Gestione e esecuzione di strumenti (es. calcolatrice)
- **Eventi SSE**: Server-Sent Events per comunicazione real-time
- **Database**: PostgreSQL per persistenza dati

### Client MCP (Python)
- **Connessione**: Inizializzazione protocollo MCP
- **Tool Calls**: Chiamate a strumenti remoti
- **Event Listener**: Ascolto eventi SSE

## Quick Start

### 1. Preparazione
```bash
# Clona il progetto (se necessario)
cd mcp-learning

# Copia configurazione di esempio
cp .env.example .env

# Modifica .env se necessario (opzionale per sviluppo)
```

### 2. Avvio con Docker
```bash
# Costruisci e avvia tutti i servizi
docker-compose up --build

# Oppure in background
docker-compose up -d --build
```

### 3. Test automatico
```bash
# Esegui test completo del sistema
./test_mcp.sh
```

### 4. Test manuale del server
```bash
# Test inizializzazione MCP
curl -X POST http://localhost:8000/api/mcp/initialize/ \
  -H "Content-Type: application/json" \
  -d '{
    "clientInfo": {"name": "Test Client", "version": "1.0.0"},
    "capabilities": {}
  }'

# Lista tools disponibili
curl http://localhost:8000/api/mcp/tools/

# Test calcolatrice
curl -X POST http://localhost:8000/api/mcp/tools/call/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "calculator",
    "arguments": {"operation": "add", "a": 10, "b": 5}
  }'
```

### 5. Client Python interattivo
```bash
# Esegui client in modalità interattiva
docker-compose exec client python mcp_client.py

# Oppure in una nuova shell
docker-compose exec client bash
python mcp_client.py
```

## Endpoints MCP Implementati

- `POST /api/mcp/initialize/` - Inizializzazione connessione MCP
- `GET /api/mcp/tools/` - Lista strumenti disponibili
- `POST /api/mcp/tools/call/` - Esecuzione strumento
- `GET /api/mcp/events/` - Stream eventi SSE

## Tool di Esempio

**Calcolatrice**: Strumento che esegue operazioni matematiche di base
- Addizione, sottrazione, moltiplicazione, divisione
- Validazione input e gestione errori
- Logging delle operazioni

## Concetti MCP Implementati

1. **Handshake Protocol**: Negoziazione capacità client-server
2. **Tool Discovery**: Enumerazione strumenti disponibili
3. **Tool Execution**: Esecuzione sicura strumenti
4. **Event Streaming**: Comunicazione asincrona via SSE
5. **Error Handling**: Gestione errori standardizzata MCP

## Sviluppo

### Aggiungere un nuovo Tool

1. Crea modello in `server/mcp_api/models.py`
2. Implementa logica in `server/mcp_api/tools/`
3. Registra in `server/mcp_api/views.py`
4. Testa con client

### Debugging e Troubleshooting

#### Logs dei servizi
```bash
# Logs del server Django
docker-compose logs -f server

# Logs del client Python
docker-compose logs -f client

# Logs del database PostgreSQL
docker-compose logs -f db

# Tutti i logs insieme
docker-compose logs -f
```

#### Accesso ai container
```bash
# Shell nel container server
docker-compose exec server bash

# Shell nel container client
docker-compose exec client bash

# Accesso al database PostgreSQL
docker-compose exec db psql -U mcp_user -d mcp_db
```

#### Django Admin
```bash
# Crea superuser per Django admin
docker-compose exec server python manage.py createsuperuser

# Accedi a: http://localhost:8000/admin/
```

#### Problemi comuni

**Errore "Connection refused":**
- Verifica che i container siano in esecuzione: `docker-compose ps`
- Controlla i logs: `docker-compose logs`

**Database non raggiungibile:**
- Aspetta che PostgreSQL sia pronto: `docker-compose logs db`
- Verifica health check: `docker-compose ps`

**Port già in uso:**
- Cambia le porte in `docker-compose.yml`
- Oppure ferma altri servizi: `sudo lsof -i :8000`

**Problemi di permessi:**
- Su Linux: `sudo chown -R $USER:$USER .`

## Prossimi Passi

- [ ] Implementare autenticazione MCP
- [ ] Aggiungere più tools di esempio
- [ ] Implementare caching Redis
- [ ] Aggiungere monitoring e metriche
- [ ] Test automatizzati

---

**Nota**: Questo è un progetto educativo per comprendere MCP. Per uso in produzione, implementare sicurezza aggiuntiva e validazioni.

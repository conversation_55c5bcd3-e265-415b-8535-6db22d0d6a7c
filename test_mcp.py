#!/usr/bin/env python3
"""
Script di test Python per il progetto MCP Learning.

Questo script testa tutte le funzionalità del server MCP implementato
con Django e Docker, fornendo un'alternativa più elegante al test bash.

Documentazione MCP: https://modelcontextprotocol.io/
"""

import json
import time
import requests
import subprocess
import sys
from typing import Dict, Any, Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.json import JSON

# Configurazione console per output colorato
console = Console()


class MCPTester:
    """
    Classe per testare il server MCP Learning.
    
    Implementa tutti i test necessari per verificare il corretto
    funzionamento del protocollo MCP e delle sue funzionalità.
    """
    
    def __init__(self, server_url: str = "http://localhost:8000"):
        """
        Inizializza il tester MCP.
        
        Args:
            server_url: URL del server MCP da testare
        """
        self.server_url = server_url
        self.session_id: Optional[str] = None
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Tester/1.0.0'
        })
    
    def print_status(self, message: str):
        """Stampa messaggio di stato."""
        console.print(f"[blue][INFO][/blue] {message}")
    
    def print_success(self, message: str):
        """Stampa messaggio di successo."""
        console.print(f"[green][SUCCESS][/green] {message}")
    
    def print_warning(self, message: str):
        """Stampa messaggio di warning."""
        console.print(f"[yellow][WARNING][/yellow] {message}")
    
    def print_error(self, message: str):
        """Stampa messaggio di errore."""
        console.print(f"[red][ERROR][/red] {message}")
    
    def check_containers(self) -> bool:
        """
        Verifica che i container Docker siano in esecuzione.
        
        Returns:
            bool: True se i container sono attivi
        """
        self.print_status("Verifico stato dei container...")
        
        try:
            result = subprocess.run(
                ["docker", "compose", "ps", "--format", "json"],
                capture_output=True,
                text=True,
                check=True
            )
            
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    containers.append(json.loads(line))
            
            running_containers = [c for c in containers if c.get('State') == 'running']
            
            if len(running_containers) >= 2:  # server e db almeno
                self.print_success("Container in esecuzione")
                return True
            else:
                self.print_error("Container non in esecuzione. Esegui: docker compose up -d")
                return False
                
        except subprocess.CalledProcessError as e:
            self.print_error(f"Errore controllo container: {e}")
            return False
        except Exception as e:
            self.print_error(f"Errore inaspettato: {e}")
            return False
    
    def test_initialization(self) -> bool:
        """
        Testa l'endpoint di inizializzazione MCP.
        
        Returns:
            bool: True se il test passa
        """
        self.print_status("Test endpoint inizializzazione MCP...")
        
        init_request = {
            "clientInfo": {
                "name": "Test Client Python",
                "version": "1.0.0"
            },
            "capabilities": {}
        }
        
        try:
            response = self.session.post(
                f"{self.server_url}/api/mcp/initialize/",
                json=init_request,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.session_id = data.get('sessionId')
                
                # Aggiorna header con session ID
                if self.session_id:
                    self.session.headers['X-MCP-Session-ID'] = self.session_id
                
                self.print_success("Inizializzazione MCP funzionante")
                self.print_status(f"Session ID: {self.session_id[:8]}...")
                return True
            else:
                self.print_error(f"Inizializzazione MCP fallita (HTTP {response.status_code})")
                console.print(response.text)
                return False
                
        except requests.exceptions.RequestException as e:
            self.print_error(f"Errore di connessione: {e}")
            return False
    
    def test_tools_listing(self) -> bool:
        """
        Testa l'endpoint per il listing dei tools.
        
        Returns:
            bool: True se il test passa
        """
        self.print_status("Test listing tools...")
        
        try:
            response = self.session.get(
                f"{self.server_url}/api/mcp/tools/",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                tools_count = len(data.get('tools', []))
                
                self.print_success("Listing tools funzionante")
                self.print_status(f"Tools disponibili: {tools_count}")
                
                # Mostra dettagli tools se presenti
                if tools_count > 0:
                    tools_table = Table(title="Tools Disponibili")
                    tools_table.add_column("Nome", style="cyan")
                    tools_table.add_column("Descrizione", style="green")
                    
                    for tool in data['tools']:
                        tools_table.add_row(
                            tool.get('name', 'N/A'),
                            tool.get('description', 'N/A')[:50] + "..."
                        )
                    
                    console.print(tools_table)
                
                return True
            else:
                self.print_error(f"Listing tools fallito (HTTP {response.status_code})")
                return False
                
        except requests.exceptions.RequestException as e:
            self.print_error(f"Errore di connessione: {e}")
            return False
    
    def test_calculator_tool(self) -> bool:
        """
        Testa il tool calcolatrice.
        
        Returns:
            bool: True se il test passa
        """
        self.print_status("Test tool calcolatrice...")
        
        calc_request = {
            "name": "calculator",
            "arguments": {
                "operation": "add",
                "a": 10,
                "b": 5
            }
        }
        
        try:
            response = self.session.post(
                f"{self.server_url}/api/mcp/tools/call/",
                json=calc_request,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if not data.get('isError', False):
                    result = data.get('content', {}).get('result')
                    self.print_success("Tool calcolatrice funzionante")
                    self.print_status(f"Risultato 10 + 5 = {result}")
                    
                    # Mostra dettagli risultato
                    result_panel = Panel(
                        JSON.from_data(data.get('content', {})),
                        title="Risultato Calcolatrice",
                        border_style="green"
                    )
                    console.print(result_panel)
                    
                    return True
                else:
                    self.print_error(f"Errore tool: {data.get('error', 'Errore sconosciuto')}")
                    return False
            else:
                self.print_error(f"Tool calcolatrice fallito (HTTP {response.status_code})")
                console.print(response.text)
                return False
                
        except requests.exceptions.RequestException as e:
            self.print_error(f"Errore di connessione: {e}")
            return False
    
    def test_server_info(self) -> bool:
        """
        Testa l'endpoint info server.
        
        Returns:
            bool: True se il test passa
        """
        self.print_status("Test info server...")
        
        try:
            response = self.session.get(
                f"{self.server_url}/api/mcp/info/",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                server_name = data.get('name', 'N/A')
                
                self.print_success("Info server funzionante")
                self.print_status(f"Server: {server_name}")
                
                # Mostra statistiche server
                stats = data.get('statistics', {})
                if stats:
                    stats_table = Table(title="Statistiche Server")
                    stats_table.add_column("Metrica", style="cyan")
                    stats_table.add_column("Valore", style="green")
                    
                    for key, value in stats.items():
                        stats_table.add_row(key.replace('_', ' ').title(), str(value))
                    
                    console.print(stats_table)
                
                return True
            else:
                self.print_error(f"Info server fallito (HTTP {response.status_code})")
                return False
                
        except requests.exceptions.RequestException as e:
            self.print_error(f"Errore di connessione: {e}")
            return False

    def test_client_python(self) -> bool:
        """
        Testa il client Python MCP.

        Returns:
            bool: True se il test passa
        """
        self.print_status("Test client Python...")

        try:
            # Test rapido del client Python
            result = subprocess.run([
                "docker", "compose", "exec", "-T", "client", "python", "-c",
                """
import sys
sys.path.append('/app')
from mcp_client import MCPClient, MCPClientConfig
import os

config = MCPClientConfig(server_url='http://server:8000')
client = MCPClient(config)

if client.initialize():
    print('✅ Client Python funzionante')
    client.disconnect()
    exit(0)
else:
    print('❌ Client Python fallito')
    exit(1)
                """
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                self.print_success("Client Python funzionante")
                console.print(result.stdout.strip())
                return True
            else:
                self.print_warning("Client Python potrebbe avere problemi")
                console.print(result.stderr.strip())
                return False

        except subprocess.TimeoutExpired:
            self.print_warning("Timeout test client Python")
            return False
        except Exception as e:
            self.print_warning(f"Errore test client Python: {e}")
            return False

    def run_all_tests(self) -> bool:
        """
        Esegue tutti i test in sequenza.

        Returns:
            bool: True se tutti i test passano
        """
        # Banner di benvenuto
        console.print(Panel.fit(
            "[bold blue]🧪 Test del progetto MCP Learning[/bold blue]\n"
            "Script Python per testare il protocollo MCP\n"
            "https://modelcontextprotocol.io/",
            border_style="blue"
        ))

        tests = [
            ("Container Docker", self.check_containers),
            ("Inizializzazione MCP", self.test_initialization),
            ("Listing Tools", self.test_tools_listing),
            ("Tool Calcolatrice", self.test_calculator_tool),
            ("Info Server", self.test_server_info),
            ("Client Python", self.test_client_python),
        ]

        passed_tests = 0
        total_tests = len(tests)

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:

            for test_name, test_func in tests:
                task = progress.add_task(f"Esecuzione {test_name}...", total=1)

                try:
                    if test_func():
                        passed_tests += 1
                        progress.update(task, completed=1, description=f"✅ {test_name}")
                    else:
                        progress.update(task, completed=1, description=f"❌ {test_name}")

                except Exception as e:
                    self.print_error(f"Errore durante {test_name}: {e}")
                    progress.update(task, completed=1, description=f"💥 {test_name}")

                time.sleep(0.5)  # Pausa per leggibilità

        # Risultati finali
        console.print()
        if passed_tests == total_tests:
            console.print(Panel(
                f"[bold green]🎉 Tutti i test completati con successo![/bold green]\n"
                f"Passati: {passed_tests}/{total_tests}",
                title="Risultati Test",
                border_style="green"
            ))

            # Istruzioni per uso manuale
            self.show_usage_instructions()
            return True
        else:
            console.print(Panel(
                f"[bold red]❌ Alcuni test sono falliti[/bold red]\n"
                f"Passati: {passed_tests}/{total_tests}",
                title="Risultati Test",
                border_style="red"
            ))
            return False

    def show_usage_instructions(self):
        """Mostra le istruzioni per l'uso manuale."""

        instructions_table = Table(title="Istruzioni per Test Manuali")
        instructions_table.add_column("Componente", style="cyan")
        instructions_table.add_column("Comando/URL", style="green")
        instructions_table.add_column("Descrizione", style="white")

        instructions_table.add_row(
            "Server API",
            "http://localhost:8000/api/mcp/",
            "Endpoint API MCP"
        )
        instructions_table.add_row(
            "Client Interattivo",
            "docker compose exec client python mcp_client.py",
            "Client Python con menu interattivo"
        )
        instructions_table.add_row(
            "Django Admin",
            "http://localhost:8000/admin/",
            "Interfaccia admin (admin/admin123)"
        )
        instructions_table.add_row(
            "Django Shell",
            "docker compose exec server python manage.py shell",
            "Shell Django interattiva"
        )

        console.print()
        console.print(instructions_table)

        logs_table = Table(title="Comandi per Logs")
        logs_table.add_column("Servizio", style="cyan")
        logs_table.add_column("Comando", style="green")

        logs_table.add_row("Server", "docker compose logs server")
        logs_table.add_row("Client", "docker compose logs client")
        logs_table.add_row("Database", "docker compose logs db")
        logs_table.add_row("Tutti", "docker compose logs")

        console.print()
        console.print(logs_table)


def main():
    """Funzione principale del tester."""

    # Parsing argomenti semplice
    server_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        server_url = sys.argv[1]

    # Crea e esegue tester
    tester = MCPTester(server_url)

    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        console.print("\n[yellow]Test interrotti dall'utente[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[red]Errore inaspettato: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()

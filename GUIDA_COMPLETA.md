# 🎓 Guida Completa al Progetto MCP Learning

Questa guida ti accompagna nell'apprendimento del **Model Context Protocol (MCP)** attraverso un progetto pratico con Django, Docker e Python.

## 📚 Cos'è il Model Context Protocol (MCP)?

Il **Model Context Protocol (MCP)** è un protocollo open-source sviluppato da Anthropic che permette agli assistenti AI di connettersi in modo sicuro a diverse fonti di dati e strumenti esterni.

### 🔗 Risorse Ufficiali
- [Documentazione MCP](https://modelcontextprotocol.io/)
- [Specifiche del protocollo](https://spec.modelcontextprotocol.io/)
- [Repository GitHub](https://github.com/modelcontextprotocol)

### 🏗️ Architettura MCP
```
┌─────────────┐    MCP Protocol    ┌─────────────┐
│   Client    │ ◄─────────────────► │   Server    │
│   (AI)      │                    │  (Tools)    │
└─────────────┘                    └─────────────┘
```

## 🚀 Avvio Rapido

### 1. Preparazione
```bash
cd mcp-learning
cp .env.example .env  # Opzionale
```

### 2. <PERSON><PERSON><PERSON> con <PERSON>er
```bash
docker compose up --build -d
```

### 3. Test Automatico
```bash
# Script Python (raccomandato)
python3 test_mcp.py

# Script Bash (alternativo)
./test_mcp.sh
```

## 🧪 Testing e Validazione

### Script di Test Python
Il file `test_mcp.py` fornisce un testing completo con interfaccia elegante:

- ✅ Verifica container Docker
- ✅ Test inizializzazione MCP
- ✅ Test discovery tools
- ✅ Test esecuzione tools
- ✅ Test client Python
- ✅ Statistiche server

### Esempio di Output
```
🧪 Test del progetto MCP Learning
✅ Container Docker
✅ Inizializzazione MCP
✅ Listing Tools
✅ Tool Calcolatrice
✅ Info Server
✅ Client Python

🎉 Tutti i test completati con successo!
```

## 🔧 Componenti del Progetto

### 1. Server Django MCP (`server/`)
- **Framework**: Django + Django REST Framework
- **Database**: PostgreSQL
- **Funzionalità**:
  - Endpoint inizializzazione MCP
  - Gestione tools (discovery ed esecuzione)
  - Server-Sent Events per comunicazione real-time
  - Admin interface per debugging

### 2. Client Python MCP (`client/`)
- **Librerie**: requests, rich, sseclient-py
- **Funzionalità**:
  - Handshake MCP automatico
  - Discovery e chiamata tools
  - Interfaccia interattiva colorata
  - Ascolto eventi SSE

### 3. Tool di Esempio: Calcolatrice
- **Operazioni**: addizione, sottrazione, moltiplicazione, divisione
- **Validazione**: input JSON Schema
- **Gestione errori**: divisione per zero, parametri non validi
- **Logging**: tutte le operazioni registrate

## 📊 Endpoints API MCP

| Endpoint | Metodo | Descrizione |
|----------|--------|-------------|
| `/api/mcp/initialize/` | POST | Handshake iniziale |
| `/api/mcp/tools/` | GET | Lista tools disponibili |
| `/api/mcp/tools/call/` | POST | Esecuzione tool |
| `/api/mcp/events/` | GET | Stream eventi SSE |
| `/api/mcp/info/` | GET | Informazioni server |

## 🎮 Modalità di Utilizzo

### 1. Client Interattivo
```bash
docker compose exec client python mcp_client.py
```

Comandi disponibili:
- `tools` - Mostra tools disponibili
- `calc` - Demo calcolatrice
- `info` - Informazioni server
- `events` - Avvia ascolto eventi
- `quit` - Esci

### 2. Django Admin
- **URL**: http://localhost:8000/admin/
- **Credenziali**: admin / admin123
- **Gestione**: Tools, Sessioni MCP, Chiamate

### 3. Django Shell
```bash
docker compose exec server python manage.py shell
```

Esempi di utilizzo:
```python
from mcp_api.models import Tool, MCPSession, ToolCall

# Visualizza tools
Tool.objects.all()

# Registra tool calcolatrice
from mcp_api.tools.calculator import register_calculator_tool
register_calculator_tool()

# Statistiche
ToolCall.objects.filter(success=True).count()
```

### 4. Test API con curl
```bash
# Inizializzazione
curl -X POST http://localhost:8000/api/mcp/initialize/ \
  -H "Content-Type: application/json" \
  -d '{"clientInfo": {"name": "Test", "version": "1.0.0"}}'

# Lista tools
curl http://localhost:8000/api/mcp/tools/

# Chiamata calcolatrice
curl -X POST http://localhost:8000/api/mcp/tools/call/ \
  -H "Content-Type: application/json" \
  -d '{"name": "calculator", "arguments": {"operation": "add", "a": 10, "b": 5}}'
```

## 🔍 Debugging e Troubleshooting

### Logs dei Servizi
```bash
docker compose logs server  # Server Django
docker compose logs client  # Client Python
docker compose logs db      # Database PostgreSQL
```

### Problemi Comuni

**Container non si avviano:**
```bash
docker compose ps
docker compose logs
```

**Errori di connessione:**
- Verifica che i container siano in esecuzione
- Controlla le porte (8000, 5432)
- Aspetta che PostgreSQL sia pronto

**Database non raggiungibile:**
```bash
docker compose exec db psql -U mcp_user -d mcp_db
```

## 📈 Estensioni Possibili

### Nuovi Tools
1. Crea file in `server/mcp_api/tools/nuovo_tool.py`
2. Implementa classe con metodi `get_tool_definition()` e `execute()`
3. Registra in `server/mcp_api/views.py`
4. Testa con client

### Funzionalità Avanzate
- [ ] Autenticazione JWT
- [ ] Rate limiting
- [ ] Caching Redis
- [ ] Monitoring Prometheus
- [ ] Test automatizzati
- [ ] CI/CD Pipeline

## 🎯 Concetti MCP Appresi

1. **Handshake Protocol**: Negoziazione capacità client-server
2. **Tool Discovery**: Enumerazione strumenti disponibili
3. **Tool Execution**: Esecuzione sicura con validazione
4. **Event Streaming**: Comunicazione asincrona via SSE
5. **Error Handling**: Gestione errori standardizzata
6. **Session Management**: Tracking connessioni client

## 📝 Prossimi Passi

1. **Studia le specifiche MCP**: https://spec.modelcontextprotocol.io/
2. **Implementa nuovi tools**: file system, API esterne, database
3. **Aggiungi autenticazione**: JWT, OAuth2
4. **Ottimizza performance**: caching, connection pooling
5. **Deploy in produzione**: Kubernetes, Docker Swarm

---

**Congratulazioni!** 🎉 Hai completato un'implementazione completa del protocollo MCP. Questo progetto ti fornisce una base solida per comprendere e implementare soluzioni MCP più avanzate.
